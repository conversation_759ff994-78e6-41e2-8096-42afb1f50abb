@echo off
title SuToDo - 智能TODO应用
color 0A

echo.
echo  ███████╗██╗   ██╗████████╗ ██████╗ ██████╗  ██████╗ 
echo  ██╔════╝██║   ██║╚══██╔══╝██╔═══██╗██╔══██╗██╔═══██╗
echo  ███████╗██║   ██║   ██║   ██║   ██║██║  ██║██║   ██║
echo  ╚════██║██║   ██║   ██║   ██║   ██║██║  ██║██║   ██║
echo  ███████║╚██████╔╝   ██║   ╚██████╔╝██████╔╝╚██████╔╝
echo  ╚══════╝ ╚═════╝    ╚═╝    ╚═════╝ ╚═════╝  ╚═════╝ 
echo.
echo                    智能TODO计划提醒软件
echo                      支持AI任务分解
echo.
echo ========================================================
echo.

echo 🔍 检查Flutter环境...
flutter --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Flutter未安装！
    echo 请先安装Flutter: https://flutter.dev/docs/get-started/install
    echo.
    pause
    exit /b 1
)
echo ✅ Flutter环境正常

echo.
echo 🔧 启用桌面支持...
flutter config --enable-windows-desktop >nul 2>&1
flutter config --enable-web >nul 2>&1
echo ✅ 桌面支持已启用

echo.
echo 📦 安装依赖...
flutter pub get >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败！
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

echo.
echo 📱 查看可用设备...
flutter devices

echo.
echo 🚀 请选择运行平台:
echo.
echo   1️⃣  Android (手机/模拟器)
echo   2️⃣  Windows (桌面应用) - 推荐
echo   3️⃣  Web (浏览器)
echo   4️⃣  查看详细设备信息
echo   5️⃣  配置AI服务
echo   6️⃣  运行测试
echo.

set /p choice="👉 请输入选择 (1-6): "

if "%choice%"=="1" (
    echo.
    echo 🚀 启动Android版本...
    flutter run -d android
) else if "%choice%"=="2" (
    echo.
    echo 🚀 启动Windows桌面版本...
    flutter run -d windows
) else if "%choice%"=="3" (
    echo.
    echo 🚀 启动Web版本...
    echo 🌐 应用将在 http://localhost:8080 运行
    start http://localhost:8080
    flutter run -d web-server --web-port 8080
) else if "%choice%"=="4" (
    echo.
    echo 📱 详细设备信息:
    flutter devices -v
    echo.
    echo 💡 使用命令: flutter run -d [设备ID] 运行到指定设备
) else if "%choice%"=="5" (
    echo.
    echo 🤖 AI服务配置:
    echo.
    echo 方法1: 设置环境变量
    echo set OPENAI_API_KEY=your_api_key_here
    echo.
    echo 方法2: 运行时指定
    echo flutter run --dart-define=OPENAI_API_KEY=your_api_key_here
    echo.
    echo 💡 如不配置AI服务，应用将使用基础计划模板
) else if "%choice%"=="6" (
    echo.
    echo 🧪 运行测试...
    flutter test
    echo.
    echo 🔍 代码分析...
    flutter analyze
) else (
    echo ❌ 无效选择！
)

echo.
echo ========================================================
echo 感谢使用 SuToDo！
echo 如有问题，请查看 docs/CROSS_PLATFORM_SETUP.md
echo ========================================================
pause
