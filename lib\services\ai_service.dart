import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/todo_task.dart';
import '../config/app_config.dart';

class AIService {

  // 生成任务计划
  static Future<AITaskPlan?> generateTaskPlan(String taskTitle, String taskDescription) async {
    try {
      final prompt = '''
请为以下任务制定详细的执行计划：

任务标题：$taskTitle
任务描述：$taskDescription

请按照以下JSON格式返回计划：
{
  "steps": [
    {
      "title": "步骤标题",
      "description": "步骤详细描述",
      "estimatedMinutes": 30
    }
  ],
  "totalEstimatedMinutes": 120,
  "reasoning": "制定此计划的理由和思路"
}

要求：
1. 将任务分解为具体的、可执行的步骤
2. 每个步骤应该有明确的目标和预期结果
3. 合理估算每个步骤所需的时间（以分钟为单位）
4. 步骤之间应该有逻辑顺序
5. 总时间应该是所有步骤时间的合理总和
6. 提供制定计划的理由说明

请只返回JSON格式的数据，不要包含其他文字。
''';

      final response = await http.post(
        Uri.parse(AppConfig.getApiUrl('/chat/completions')),
        headers: AppConfig.apiHeaders,
        body: jsonEncode({
          'model': AppConfig.openaiModel,
          'messages': [
            {
              'role': 'system',
              'content': '你是一个专业的任务规划助手，擅长将复杂任务分解为具体可执行的步骤，并准确估算时间。'
            },
            {
              'role': 'user',
              'content': prompt,
            }
          ],
          'max_tokens': 1000,
          'temperature': 0.7,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'];
        
        // 解析AI返回的JSON
        final planData = jsonDecode(content);
        
        final steps = (planData['steps'] as List).map((stepData) {
          return TaskStep(
            title: stepData['title'],
            description: stepData['description'],
            estimatedMinutes: stepData['estimatedMinutes'],
          );
        }).toList();

        return AITaskPlan(
          steps: steps,
          totalEstimatedMinutes: planData['totalEstimatedMinutes'],
          reasoning: planData['reasoning'],
        );
      } else {
        print('AI API Error: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      print('Error generating task plan: $e');
      return _generateFallbackPlan(taskTitle, taskDescription);
    }
  }

  // 优化现有计划
  static Future<AITaskPlan?> optimizeTaskPlan(AITaskPlan currentPlan, String feedback) async {
    try {
      final prompt = '''
请根据用户反馈优化以下任务计划：

当前计划：
${jsonEncode(currentPlan.toJson())}

用户反馈：$feedback

请返回优化后的计划，格式与原计划相同。要求：
1. 根据反馈调整步骤内容或顺序
2. 重新评估时间估算
3. 保持计划的可执行性
4. 在reasoning中说明优化的理由

请只返回JSON格式的数据。
''';

      final response = await http.post(
        Uri.parse(AppConfig.getApiUrl('/chat/completions')),
        headers: AppConfig.apiHeaders,
        body: jsonEncode({
          'model': AppConfig.openaiModel,
          'messages': [
            {
              'role': 'system',
              'content': '你是一个专业的任务规划助手，擅长根据反馈优化任务计划。'
            },
            {
              'role': 'user',
              'content': prompt,
            }
          ],
          'max_tokens': 1000,
          'temperature': 0.7,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content = data['choices'][0]['message']['content'];
        final planData = jsonDecode(content);
        
        final steps = (planData['steps'] as List).map((stepData) {
          return TaskStep(
            title: stepData['title'],
            description: stepData['description'],
            estimatedMinutes: stepData['estimatedMinutes'],
          );
        }).toList();

        return AITaskPlan(
          steps: steps,
          totalEstimatedMinutes: planData['totalEstimatedMinutes'],
          reasoning: planData['reasoning'],
        );
      }
    } catch (e) {
      print('Error optimizing task plan: $e');
    }
    return null;
  }

  // 生成备用计划（当AI服务不可用时）
  static AITaskPlan _generateFallbackPlan(String taskTitle, String taskDescription) {
    // 基于任务复杂度的简单估算
    final words = taskDescription.split(' ').length;
    final estimatedMinutes = (words * 2).clamp(15, 240); // 每个词2分钟，最少15分钟，最多4小时

    final steps = <TaskStep>[];
    
    // 通用步骤模板
    steps.add(TaskStep(
      title: '准备和规划',
      description: '收集必要的资源和信息，制定详细的执行计划',
      estimatedMinutes: (estimatedMinutes * 0.2).round(),
    ));

    steps.add(TaskStep(
      title: '执行主要工作',
      description: '按照计划执行任务的核心内容',
      estimatedMinutes: (estimatedMinutes * 0.6).round(),
    ));

    steps.add(TaskStep(
      title: '检查和完善',
      description: '检查工作成果，进行必要的修改和完善',
      estimatedMinutes: (estimatedMinutes * 0.2).round(),
    ));

    return AITaskPlan(
      steps: steps,
      totalEstimatedMinutes: estimatedMinutes,
      reasoning: '基于任务描述的复杂度生成的通用计划模板',
    );
  }

  // 检查API密钥是否配置
  static bool isConfigured() {
    return AppConfig.isAIConfigured;
  }

  // 估算任务完成时间
  static DateTime? estimateCompletionTime(TodoTask task) {
    if (task.aiPlan == null) return null;
    
    final now = DateTime.now();
    return now.add(Duration(minutes: task.aiPlan!.totalEstimatedMinutes));
  }

  // 根据历史数据调整时间估算
  static int adjustTimeEstimate(int originalEstimate, List<TodoTask> completedTasks) {
    if (completedTasks.isEmpty) return originalEstimate;

    // 计算历史任务的平均完成时间与估算时间的比率
    double totalRatio = 0;
    int validTasks = 0;

    for (final task in completedTasks) {
      if (task.aiPlan != null && task.startedAt != null && task.completedAt != null) {
        final actualMinutes = task.completedAt!.difference(task.startedAt!).inMinutes;
        final estimatedMinutes = task.aiPlan!.totalEstimatedMinutes;
        
        if (estimatedMinutes > 0) {
          totalRatio += actualMinutes / estimatedMinutes;
          validTasks++;
        }
      }
    }

    if (validTasks > 0) {
      final averageRatio = totalRatio / validTasks;
      return (originalEstimate * averageRatio).round();
    }

    return originalEstimate;
  }
}
