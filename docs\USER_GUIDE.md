# SuToDo 用户使用指南

## 快速开始

### 1. 安装应用
- 从GitHub下载最新版本的APK文件
- 在Android设备上安装应用
- 首次启动时，应用会请求通知权限，建议允许以获得最佳体验

### 2. 创建第一个任务
1. 点击主界面右下角的"新建任务"按钮
2. 填写任务标题（必填）和描述（必填）
3. 选择优先级：低、中、高、紧急
4. 设置截止时间（可选）
5. 添加标签（可选，用逗号分隔）
6. 保存任务

## 核心功能

### AI智能计划

#### 启用AI功能
1. 在创建任务时，开启"AI智能计划"开关
2. 填写详细的任务描述
3. 点击"生成AI计划"按钮
4. AI会自动分析任务并生成执行步骤

#### AI计划包含内容
- **步骤分解**: 将复杂任务分解为具体可执行的步骤
- **时间估算**: 每个步骤的预计完成时间
- **总体规划**: 整个任务的总预计时间
- **执行建议**: AI对任务执行的分析和建议

#### 使用AI计划
1. 开始任务后，按照AI生成的步骤逐一执行
2. 完成每个步骤时，点击步骤旁的完成按钮
3. 系统会跟踪进度并显示剩余时间
4. 如果进度落后，会显示延迟提醒

### 任务管理

#### 任务状态
- **待开始**: 新创建的任务，尚未开始执行
- **进行中**: 正在执行的任务
- **已完成**: 已经完成的任务
- **已取消**: 取消执行的任务

#### 任务操作
- **开始任务**: 将待开始的任务标记为进行中
- **完成任务**: 将进行中的任务标记为已完成
- **暂停任务**: 将进行中的任务暂停，状态变为待开始
- **取消任务**: 取消任务执行

#### 优先级管理
- **低优先级**: 不紧急的日常任务
- **中优先级**: 一般重要的任务（默认）
- **高优先级**: 重要且需要优先处理的任务
- **紧急**: 需要立即处理的紧急任务

### 搜索和筛选

#### 搜索功能
1. 点击主界面右上角的搜索图标
2. 在搜索框中输入关键词
3. 系统会搜索任务标题、描述和标签
4. 支持模糊搜索和实时搜索

#### 筛选功能
1. 启用搜索后，会显示筛选选项
2. 可按任务状态筛选：待开始、进行中、已完成、已取消
3. 可按优先级筛选：低、中、高、紧急
4. 可组合多个筛选条件
5. 点击"清除筛选"重置所有筛选条件

### 提醒通知

#### 自动提醒
- **截止提醒**: 任务到期前30分钟自动提醒
- **到期提醒**: 任务到期时自动提醒
- **步骤提醒**: AI计划中每个步骤的预计完成时间提醒

#### 通知设置
1. 确保在系统设置中允许SuToDo发送通知
2. 建议开启声音和振动提醒
3. 可在系统设置中自定义通知样式

### 数据统计

#### 统计信息
- **总任务数**: 所有创建的任务数量
- **完成率**: 已完成任务占总任务的百分比
- **进行中任务**: 当前正在执行的任务数量
- **今日到期**: 今天到期的任务数量
- **逾期任务**: 已经过期但未完成的任务数量

#### 进度跟踪
- 实时显示任务完成进度
- 对比预计时间和实际时间
- 显示任务延迟情况
- 提供效率分析

## 高级功能

### 标签系统
- 为任务添加多个标签，用逗号分隔
- 标签可用于分类和搜索
- 常用标签：工作、学习、生活、重要、紧急等

### 时间管理
- 设置任务截止时间
- 查看预计完成时间
- 跟踪实际执行时间
- 分析时间使用效率

### 数据备份
- 所有数据存储在本地SQLite数据库
- 建议定期备份应用数据
- 可通过系统备份功能保护数据

## 使用技巧

### 提高AI计划质量
1. **详细描述**: 任务描述越详细，AI生成的计划越准确
2. **明确目标**: 清楚说明任务的最终目标和期望结果
3. **包含背景**: 提供任务的背景信息和约束条件
4. **具体要求**: 说明具体的要求和标准

### 高效任务管理
1. **合理分类**: 使用标签对任务进行分类管理
2. **优先级排序**: 根据重要性和紧急性设置优先级
3. **定期回顾**: 定期查看和更新任务状态
4. **及时完成**: 按时完成任务，避免积压

### 时间规划建议
1. **预留缓冲**: 为任务预留额外的时间缓冲
2. **分解大任务**: 将大任务分解为小的可管理步骤
3. **专注执行**: 执行任务时保持专注，避免分心
4. **及时调整**: 根据实际情况调整计划和时间安排

## 常见问题

### Q: AI功能无法使用怎么办？
A: AI功能需要配置OpenAI API密钥。如果未配置，应用会使用基础的计划模板。

### Q: 通知不工作怎么办？
A: 请检查系统设置中是否允许SuToDo发送通知，并确保开启了相关权限。

### Q: 数据会丢失吗？
A: 所有数据存储在本地，不会上传到服务器。建议定期备份设备数据。

### Q: 如何删除任务？
A: 在任务详情页面，点击右上角菜单，选择"删除"选项。

### Q: 可以编辑已创建的任务吗？
A: 当前版本暂不支持编辑功能，该功能将在后续版本中添加。

## 反馈和支持

如果您在使用过程中遇到问题或有改进建议，请通过以下方式联系我们：
- 在GitHub项目页面提交Issue
- 发送邮件至开发者邮箱

感谢您使用SuToDo！
