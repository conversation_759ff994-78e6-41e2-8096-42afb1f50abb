import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../providers/todo_provider.dart';
import '../providers/ai_provider.dart';
import '../models/todo_task.dart';
import '../widgets/ai_plan_widget.dart';

class AddTaskScreen extends StatefulWidget {
  const AddTaskScreen({super.key});

  @override
  State<AddTaskScreen> createState() => _AddTaskScreenState();
}

class _AddTaskScreenState extends State<AddTaskScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagsController = TextEditingController();
  
  TaskPriority _priority = TaskPriority.medium;
  DateTime? _dueDate;
  AITaskPlan? _aiPlan;
  bool _useAI = false;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('新建任务'),
        actions: [
          TextButton(
            onPressed: _saveTask,
            child: const Text('保存'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 任务标题
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: '任务标题 *',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入任务标题';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // 任务描述
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: '任务描述',
                  border: OutlineInputBorder(),
                  alignLabelWithHint: true,
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入任务描述';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // 优先级选择
              const Text(
                '优先级',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: TaskPriority.values.map((priority) {
                  return ChoiceChip(
                    label: Text(_getPriorityText(priority)),
                    selected: _priority == priority,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _priority = priority;
                        });
                      }
                    },
                    selectedColor: _getPriorityColor(priority).withOpacity(0.3),
                    labelStyle: TextStyle(
                      color: _priority == priority ? _getPriorityColor(priority) : null,
                      fontWeight: _priority == priority ? FontWeight.bold : null,
                    ),
                  );
                }).toList(),
              ),
              
              const SizedBox(height: 16),
              
              // 截止日期
              ListTile(
                leading: const Icon(Icons.calendar_today),
                title: Text(_dueDate == null 
                    ? '设置截止日期' 
                    : '截止日期: ${DateFormat('yyyy-MM-dd HH:mm').format(_dueDate!)}'),
                trailing: _dueDate != null 
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          setState(() {
                            _dueDate = null;
                          });
                        },
                      )
                    : const Icon(Icons.arrow_forward_ios),
                onTap: _selectDueDate,
                contentPadding: EdgeInsets.zero,
              ),
              
              const SizedBox(height: 16),
              
              // 标签
              TextFormField(
                controller: _tagsController,
                decoration: const InputDecoration(
                  labelText: '标签 (用逗号分隔)',
                  border: OutlineInputBorder(),
                  hintText: '例如: 工作, 重要, 紧急',
                ),
              ),
              
              const SizedBox(height: 24),
              
              // AI计划选项
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.smart_toy, color: Colors.blue),
                          const SizedBox(width: 8),
                          const Text(
                            'AI智能计划',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          Switch(
                            value: _useAI,
                            onChanged: (value) {
                              setState(() {
                                _useAI = value;
                                if (!value) {
                                  _aiPlan = null;
                                }
                              });
                            },
                          ),
                        ],
                      ),
                      if (_useAI) ...[
                        const SizedBox(height: 8),
                        const Text(
                          'AI将根据任务内容自动生成详细的执行计划和时间估算',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 16),
                        Consumer<AIProvider>(
                          builder: (context, aiProvider, child) {
                            if (!aiProvider.isAIConfigured) {
                              return Container(
                                padding: const EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.orange.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(color: Colors.orange.withOpacity(0.3)),
                                ),
                                child: const Row(
                                  children: [
                                    Icon(Icons.warning, color: Colors.orange),
                                    SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        'AI服务未配置，将使用基础计划模板',
                                        style: TextStyle(color: Colors.orange),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }
                            
                            return Column(
                              children: [
                                ElevatedButton.icon(
                                  onPressed: aiProvider.isLoading ? null : _generateAIPlan,
                                  icon: aiProvider.isLoading 
                                      ? const SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(strokeWidth: 2),
                                        )
                                      : const Icon(Icons.auto_awesome),
                                  label: Text(aiProvider.isLoading ? '生成中...' : '生成AI计划'),
                                ),
                                if (aiProvider.lastError != null) ...[
                                  const SizedBox(height: 8),
                                  Text(
                                    aiProvider.lastError!,
                                    style: const TextStyle(color: Colors.red, fontSize: 12),
                                  ),
                                ],
                              ],
                            );
                          },
                        ),
                        if (_aiPlan != null) ...[
                          const SizedBox(height: 16),
                          AIPlanWidget(
                            plan: _aiPlan!,
                            isEditable: true,
                            onPlanChanged: (updatedPlan) {
                              setState(() {
                                _aiPlan = updatedPlan;
                              });
                            },
                          ),
                        ],
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getPriorityText(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return '低';
      case TaskPriority.medium:
        return '中';
      case TaskPriority.high:
        return '高';
      case TaskPriority.urgent:
        return '紧急';
    }
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return Colors.grey;
      case TaskPriority.medium:
        return Colors.blue;
      case TaskPriority.high:
        return Colors.orange;
      case TaskPriority.urgent:
        return Colors.red;
    }
  }

  Future<void> _selectDueDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null && mounted) {
      final time = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(_dueDate ?? DateTime.now().add(const Duration(hours: 1))),
      );
      
      if (time != null) {
        setState(() {
          _dueDate = DateTime(
            date.year,
            date.month,
            date.day,
            time.hour,
            time.minute,
          );
        });
      }
    }
  }

  Future<void> _generateAIPlan() async {
    if (_titleController.text.trim().isEmpty || _descriptionController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请先填写任务标题和描述')),
      );
      return;
    }

    final aiProvider = context.read<AIProvider>();
    final plan = await aiProvider.generateTaskPlan(
      _titleController.text.trim(),
      _descriptionController.text.trim(),
    );

    if (plan != null) {
      setState(() {
        _aiPlan = plan;
      });
    }
  }

  Future<void> _saveTask() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final tags = _tagsController.text
        .split(',')
        .map((tag) => tag.trim())
        .where((tag) => tag.isNotEmpty)
        .toList();

    final task = TodoTask(
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      priority: _priority,
      dueDate: _dueDate,
      tags: tags,
      aiPlan: _useAI ? _aiPlan : null,
      estimatedMinutes: _aiPlan?.totalEstimatedMinutes ?? 0,
    );

    try {
      await context.read<TodoProvider>().addTask(task);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('任务创建成功')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('创建任务失败: $e')),
        );
      }
    }
  }
}
