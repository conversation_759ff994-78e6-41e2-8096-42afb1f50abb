#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

clear

echo -e "${BLUE}"
echo "  ███████╗██╗   ██╗████████╗ ██████╗ ██████╗  ██████╗ "
echo "  ██╔════╝██║   ██║╚══██╔══╝██╔═══██╗██╔══██╗██╔═══██╗"
echo "  ███████╗██║   ██║   ██║   ██║   ██║██║  ██║██║   ██║"
echo "  ╚════██║██║   ██║   ██║   ██║   ██║██║  ██║██║   ██║"
echo "  ███████║╚██████╔╝   ██║   ╚██████╔╝██████╔╝╚██████╔╝"
echo "  ╚══════╝ ╚═════╝    ╚═╝    ╚═════╝ ╚═════╝  ╚═════╝ "
echo -e "${NC}"
echo
echo -e "${YELLOW}                    智能TODO计划提醒软件${NC}"
echo -e "${YELLOW}                      支持AI任务分解${NC}"
echo
echo "========================================================"
echo

echo -e "${BLUE}🔍 检查Flutter环境...${NC}"
if ! command -v flutter &> /dev/null; then
    echo -e "${RED}❌ Flutter未安装！${NC}"
    echo "请先安装Flutter: https://flutter.dev/docs/get-started/install"
    echo
    exit 1
fi
echo -e "${GREEN}✅ Flutter环境正常${NC}"

echo
echo -e "${BLUE}🔧 启用桌面支持...${NC}"
flutter config --enable-macos-desktop > /dev/null 2>&1
flutter config --enable-linux-desktop > /dev/null 2>&1
flutter config --enable-web > /dev/null 2>&1
echo -e "${GREEN}✅ 桌面支持已启用${NC}"

echo
echo -e "${BLUE}📦 安装依赖...${NC}"
if ! flutter pub get > /dev/null 2>&1; then
    echo -e "${RED}❌ 依赖安装失败！${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 依赖安装完成${NC}"

echo
echo -e "${BLUE}📱 查看可用设备...${NC}"
flutter devices

echo
echo -e "${YELLOW}🚀 请选择运行平台:${NC}"
echo
echo "  1️⃣  Android (手机/模拟器)"
echo "  2️⃣  iOS (iPhone/iPad) - 需要macOS"
echo "  3️⃣  macOS (桌面应用) - 推荐"
echo "  4️⃣  Linux (桌面应用)"
echo "  5️⃣  Web (浏览器)"
echo "  6️⃣  查看详细设备信息"
echo "  7️⃣  配置AI服务"
echo "  8️⃣  运行测试"
echo

read -p "👉 请输入选择 (1-8): " choice

case $choice in
    1)
        echo
        echo -e "${GREEN}🚀 启动Android版本...${NC}"
        flutter run -d android
        ;;
    2)
        echo
        echo -e "${GREEN}🚀 启动iOS版本...${NC}"
        flutter run -d ios
        ;;
    3)
        echo
        echo -e "${GREEN}🚀 启动macOS桌面版本...${NC}"
        flutter run -d macos
        ;;
    4)
        echo
        echo -e "${GREEN}🚀 启动Linux桌面版本...${NC}"
        flutter run -d linux
        ;;
    5)
        echo
        echo -e "${GREEN}🚀 启动Web版本...${NC}"
        echo -e "${BLUE}🌐 应用将在 http://localhost:8080 运行${NC}"
        if command -v open &> /dev/null; then
            open http://localhost:8080
        elif command -v xdg-open &> /dev/null; then
            xdg-open http://localhost:8080
        fi
        flutter run -d web-server --web-port 8080
        ;;
    6)
        echo
        echo -e "${BLUE}📱 详细设备信息:${NC}"
        flutter devices -v
        echo
        echo -e "${YELLOW}💡 使用命令: flutter run -d [设备ID] 运行到指定设备${NC}"
        ;;
    7)
        echo
        echo -e "${YELLOW}🤖 AI服务配置:${NC}"
        echo
        echo "方法1: 设置环境变量"
        echo "export OPENAI_API_KEY=your_api_key_here"
        echo
        echo "方法2: 运行时指定"
        echo "flutter run --dart-define=OPENAI_API_KEY=your_api_key_here"
        echo
        echo -e "${BLUE}💡 如不配置AI服务，应用将使用基础计划模板${NC}"
        ;;
    8)
        echo
        echo -e "${BLUE}🧪 运行测试...${NC}"
        flutter test
        echo
        echo -e "${BLUE}🔍 代码分析...${NC}"
        flutter analyze
        ;;
    *)
        echo -e "${RED}❌ 无效选择！${NC}"
        ;;
esac

echo
echo "========================================================"
echo -e "${GREEN}感谢使用 SuToDo！${NC}"
echo "如有问题，请查看 docs/CROSS_PLATFORM_SETUP.md"
echo "========================================================"
