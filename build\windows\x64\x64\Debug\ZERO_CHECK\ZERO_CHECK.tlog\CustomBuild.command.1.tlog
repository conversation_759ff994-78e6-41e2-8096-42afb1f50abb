^C:\USERS\<USER>\DESKTOP\SUTODO\BUILD\WINDOWS\X64\CMAKEFILES\A07B3F86C0C0600C7ADBEE6311D8477B\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Professional\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SC:/Users/<USER>/Desktop/SuToDo/windows -BC:/Users/<USER>/Desktop/SuToDo/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Desktop/SuToDo/build/windows/x64/sutodo.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
