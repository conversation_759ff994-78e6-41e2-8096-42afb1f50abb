@echo off
echo ========================================
echo SuToDo 构建脚本
echo ========================================
echo.

echo 请选择构建类型:
echo 1. Debug APK
echo 2. Release APK
echo 3. Debug iOS
echo 4. Release iOS
echo 5. 运行测试
echo 6. 代码分析
echo.

set /p choice="请输入选择 (1-6): "

if "%choice%"=="1" (
    echo 构建 Debug APK...
    flutter build apk --debug
    echo Debug APK 构建完成！
    echo 输出路径: build\app\outputs\flutter-apk\app-debug.apk
) else if "%choice%"=="2" (
    echo 构建 Release APK...
    flutter build apk --release
    echo Release APK 构建完成！
    echo 输出路径: build\app\outputs\flutter-apk\app-release.apk
) else if "%choice%"=="3" (
    echo 构建 Debug iOS...
    flutter build ios --debug
    echo Debug iOS 构建完成！
) else if "%choice%"=="4" (
    echo 构建 Release iOS...
    flutter build ios --release
    echo Release iOS 构建完成！
) else if "%choice%"=="5" (
    echo 运行测试...
    flutter test
    echo 测试完成！
) else if "%choice%"=="6" (
    echo 运行代码分析...
    flutter analyze
    echo 代码分析完成！
) else (
    echo 无效选择！
    goto :eof
)

echo.
echo ========================================
echo 操作完成！
echo ========================================
pause
