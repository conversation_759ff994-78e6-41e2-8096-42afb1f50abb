@echo off
echo ========================================
echo SuToDo 项目设置脚本
echo ========================================
echo.

echo 1. 检查Flutter环境...
flutter --version
if %errorlevel% neq 0 (
    echo 错误: Flutter未安装或未添加到PATH中
    echo 请先安装Flutter: https://flutter.dev/docs/get-started/install
    pause
    exit /b 1
)

echo.
echo 2. 安装项目依赖...
flutter pub get
if %errorlevel% neq 0 (
    echo 错误: 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 3. 检查设备连接...
flutter devices

echo.
echo 4. AI服务配置 (可选)
echo 如需使用AI功能，请设置环境变量 OPENAI_API_KEY
echo 例如: set OPENAI_API_KEY=your_api_key_here
echo 或者在系统环境变量中设置
echo.

echo 5. 运行应用
echo 使用以下命令运行应用:
echo flutter run
echo.
echo 或者使用以下命令运行并设置API密钥:
echo flutter run --dart-define=OPENAI_API_KEY=your_api_key_here
echo.

echo ========================================
echo 设置完成！
echo ========================================
pause
