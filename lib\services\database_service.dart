import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/todo_task.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  static DatabaseService get instance => _instance;
  DatabaseService._internal();

  Database? _database;

  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final databasePath = await getDatabasesPath();
    final path = join(databasePath, 'sutodo.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE tasks (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        createdAt INTEGER NOT NULL,
        dueDate INTEGER,
        priority INTEGER NOT NULL,
        status INTEGER NOT NULL,
        tags TEXT,
        aiPlan TEXT,
        estimatedMinutes INTEGER DEFAULT 0,
        completedAt INTEGER,
        startedAt INTEGER
      )
    ''');
  }

  // 任务相关操作
  Future<String> insertTask(TodoTask task) async {
    final db = await database;
    final taskMap = task.toMap();
    
    // 将 aiPlan 转换为 JSON 字符串
    if (taskMap['aiPlan'] != null) {
      taskMap['aiPlan'] = jsonEncode(taskMap['aiPlan']);
    }
    
    await db.insert('tasks', taskMap);
    return task.id;
  }

  Future<List<TodoTask>> getAllTasks() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      orderBy: 'createdAt DESC',
    );

    return maps.map((map) {
      // 将 JSON 字符串转换回 aiPlan 对象
      if (map['aiPlan'] != null && map['aiPlan'].toString().isNotEmpty) {
        try {
          map['aiPlan'] = jsonDecode(map['aiPlan']);
        } catch (e) {
          map['aiPlan'] = null;
        }
      }
      return TodoTask.fromMap(map);
    }).toList();
  }

  Future<TodoTask?> getTaskById(String id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isEmpty) return null;

    final map = maps.first;
    if (map['aiPlan'] != null && map['aiPlan'].toString().isNotEmpty) {
      try {
        map['aiPlan'] = jsonDecode(map['aiPlan']);
      } catch (e) {
        map['aiPlan'] = null;
      }
    }

    return TodoTask.fromMap(map);
  }

  Future<void> updateTask(TodoTask task) async {
    final db = await database;
    final taskMap = task.toMap();
    
    // 将 aiPlan 转换为 JSON 字符串
    if (taskMap['aiPlan'] != null) {
      taskMap['aiPlan'] = jsonEncode(taskMap['aiPlan']);
    }
    
    await db.update(
      'tasks',
      taskMap,
      where: 'id = ?',
      whereArgs: [task.id],
    );
  }

  Future<void> deleteTask(String id) async {
    final db = await database;
    await db.delete(
      'tasks',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<TodoTask>> getTasksByStatus(TaskStatus status) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'status = ?',
      whereArgs: [status.index],
      orderBy: 'createdAt DESC',
    );

    return maps.map((map) {
      if (map['aiPlan'] != null && map['aiPlan'].toString().isNotEmpty) {
        try {
          map['aiPlan'] = jsonDecode(map['aiPlan']);
        } catch (e) {
          map['aiPlan'] = null;
        }
      }
      return TodoTask.fromMap(map);
    }).toList();
  }

  Future<List<TodoTask>> getTasksByPriority(TaskPriority priority) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'priority = ?',
      whereArgs: [priority.index],
      orderBy: 'createdAt DESC',
    );

    return maps.map((map) {
      if (map['aiPlan'] != null && map['aiPlan'].toString().isNotEmpty) {
        try {
          map['aiPlan'] = jsonDecode(map['aiPlan']);
        } catch (e) {
          map['aiPlan'] = null;
        }
      }
      return TodoTask.fromMap(map);
    }).toList();
  }

  Future<List<TodoTask>> searchTasks(String query) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'tasks',
      where: 'title LIKE ? OR description LIKE ? OR tags LIKE ?',
      whereArgs: ['%$query%', '%$query%', '%$query%'],
      orderBy: 'createdAt DESC',
    );

    return maps.map((map) {
      if (map['aiPlan'] != null && map['aiPlan'].toString().isNotEmpty) {
        try {
          map['aiPlan'] = jsonDecode(map['aiPlan']);
        } catch (e) {
          map['aiPlan'] = null;
        }
      }
      return TodoTask.fromMap(map);
    }).toList();
  }

  Future<void> clearAllTasks() async {
    final db = await database;
    await db.delete('tasks');
  }

  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
