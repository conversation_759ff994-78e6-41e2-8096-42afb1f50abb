import 'package:flutter/foundation.dart';
import '../models/todo_task.dart';
import '../services/database_service.dart';
import '../services/notification_service.dart';

class TodoProvider extends ChangeNotifier {
  List<TodoTask> _tasks = [];
  bool _isLoading = false;
  String _searchQuery = '';
  TaskStatus? _filterStatus;
  TaskPriority? _filterPriority;

  List<TodoTask> get tasks => _getFilteredTasks();
  bool get isLoading => _isLoading;
  String get searchQuery => _searchQuery;
  TaskStatus? get filterStatus => _filterStatus;
  TaskPriority? get filterPriority => _filterPriority;

  List<TodoTask> get pendingTasks => 
      _tasks.where((task) => task.status == TaskStatus.pending).toList();
  
  List<TodoTask> get inProgressTasks => 
      _tasks.where((task) => task.status == TaskStatus.inProgress).toList();
  
  List<TodoTask> get completedTasks => 
      _tasks.where((task) => task.status == TaskStatus.completed).toList();

  int get totalTasks => _tasks.length;
  int get completedTasksCount => completedTasks.length;
  double get completionRate => 
      totalTasks > 0 ? completedTasksCount / totalTasks : 0.0;

  List<TodoTask> _getFilteredTasks() {
    List<TodoTask> filtered = List.from(_tasks);

    // 按状态筛选
    if (_filterStatus != null) {
      filtered = filtered.where((task) => task.status == _filterStatus).toList();
    }

    // 按优先级筛选
    if (_filterPriority != null) {
      filtered = filtered.where((task) => task.priority == _filterPriority).toList();
    }

    // 按搜索关键词筛选
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((task) =>
          task.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          task.description.toLowerCase().contains(_searchQuery.toLowerCase()) ||
          task.tags.any((tag) => tag.toLowerCase().contains(_searchQuery.toLowerCase()))
      ).toList();
    }

    // 排序：优先级高的在前，然后按创建时间倒序
    filtered.sort((a, b) {
      // 首先按状态排序：进行中 > 待开始 > 已完成 > 已取消
      final statusOrder = {
        TaskStatus.inProgress: 0,
        TaskStatus.pending: 1,
        TaskStatus.completed: 2,
        TaskStatus.cancelled: 3,
      };
      
      final statusComparison = statusOrder[a.status]!.compareTo(statusOrder[b.status]!);
      if (statusComparison != 0) return statusComparison;

      // 然后按优先级排序
      final priorityComparison = b.priority.index.compareTo(a.priority.index);
      if (priorityComparison != 0) return priorityComparison;

      // 最后按创建时间排序
      return b.createdAt.compareTo(a.createdAt);
    });

    return filtered;
  }

  Future<void> loadTasks() async {
    _isLoading = true;
    notifyListeners();

    try {
      _tasks = await DatabaseService.instance.getAllTasks();
    } catch (e) {
      print('Error loading tasks: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> addTask(TodoTask task) async {
    try {
      await DatabaseService.instance.insertTask(task);
      _tasks.add(task);
      
      // 设置提醒
      if (task.dueDate != null) {
        await NotificationService.scheduleTaskReminder(task);
      }
      
      notifyListeners();
    } catch (e) {
      print('Error adding task: $e');
      rethrow;
    }
  }

  Future<void> updateTask(TodoTask updatedTask) async {
    try {
      await DatabaseService.instance.updateTask(updatedTask);
      
      final index = _tasks.indexWhere((task) => task.id == updatedTask.id);
      if (index != -1) {
        final oldTask = _tasks[index];
        _tasks[index] = updatedTask;
        
        // 更新通知
        await NotificationService.cancelTaskNotifications(updatedTask.id);
        
        if (updatedTask.dueDate != null && updatedTask.status != TaskStatus.completed) {
          await NotificationService.scheduleTaskReminder(updatedTask);
        }
        
        // 如果任务开始执行且有AI计划，设置步骤提醒
        if (updatedTask.status == TaskStatus.inProgress && 
            updatedTask.aiPlan != null && 
            oldTask.status != TaskStatus.inProgress) {
          await NotificationService.scheduleAIPlanReminders(updatedTask);
        }
        
        notifyListeners();
      }
    } catch (e) {
      print('Error updating task: $e');
      rethrow;
    }
  }

  Future<void> deleteTask(String taskId) async {
    try {
      await DatabaseService.instance.deleteTask(taskId);
      await NotificationService.cancelTaskNotifications(taskId);
      
      _tasks.removeWhere((task) => task.id == taskId);
      notifyListeners();
    } catch (e) {
      print('Error deleting task: $e');
      rethrow;
    }
  }

  Future<void> startTask(String taskId) async {
    final task = _tasks.firstWhere((t) => t.id == taskId);
    final updatedTask = task.copyWith(
      status: TaskStatus.inProgress,
      startedAt: DateTime.now(),
    );
    await updateTask(updatedTask);
  }

  Future<void> completeTask(String taskId) async {
    final task = _tasks.firstWhere((t) => t.id == taskId);
    final updatedTask = task.copyWith(
      status: TaskStatus.completed,
      completedAt: DateTime.now(),
    );
    await updateTask(updatedTask);
  }

  Future<void> pauseTask(String taskId) async {
    final task = _tasks.firstWhere((t) => t.id == taskId);
    final updatedTask = task.copyWith(status: TaskStatus.pending);
    await updateTask(updatedTask);
  }

  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void setStatusFilter(TaskStatus? status) {
    _filterStatus = status;
    notifyListeners();
  }

  void setPriorityFilter(TaskPriority? priority) {
    _filterPriority = priority;
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _filterStatus = null;
    _filterPriority = null;
    notifyListeners();
  }

  TodoTask? getTaskById(String id) {
    try {
      return _tasks.firstWhere((task) => task.id == id);
    } catch (e) {
      return null;
    }
  }

  List<TodoTask> getTasksDueToday() {
    final today = DateTime.now();
    return _tasks.where((task) {
      if (task.dueDate == null) return false;
      return task.dueDate!.year == today.year &&
             task.dueDate!.month == today.month &&
             task.dueDate!.day == today.day;
    }).toList();
  }

  List<TodoTask> getOverdueTasks() {
    final now = DateTime.now();
    return _tasks.where((task) {
      if (task.dueDate == null || task.status == TaskStatus.completed) return false;
      return task.dueDate!.isBefore(now);
    }).toList();
  }

  Future<void> refreshTasks() async {
    await loadTasks();
  }
}
