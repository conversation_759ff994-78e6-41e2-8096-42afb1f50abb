import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest.dart' as tz;

import 'providers/todo_provider.dart';
import 'providers/ai_provider.dart';
import 'screens/home_screen.dart';
import 'services/notification_service.dart';
import 'services/database_service.dart';

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化时区数据
  tz.initializeTimeZones();
  
  // 初始化通知服务
  await NotificationService.initialize();
  
  // 初始化数据库
  await DatabaseService.instance.database;
  
  runApp(const SuToDoDesktopApp());
}

class SuToDoDesktopApp extends StatelessWidget {
  const SuToDoDesktopApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => TodoProvider()),
        ChangeNotifierProvider(create: (_) => AIProvider()),
      ],
      child: MaterialApp(
        title: 'SuToDo - Smart TODO App',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF2196F3),
            brightness: Brightness.light,
          ),
          appBarTheme: const AppBarTheme(
            elevation: 0,
            centerTitle: true,
          ),
          cardTheme: CardTheme(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          floatingActionButtonTheme: const FloatingActionButtonThemeData(
            elevation: 4,
          ),
          // 桌面优化
          visualDensity: VisualDensity.adaptivePlatformDensity,
          tooltipTheme: const TooltipThemeData(
            waitDuration: Duration(milliseconds: 500),
          ),
        ),
        darkTheme: ThemeData(
          useMaterial3: true,
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF2196F3),
            brightness: Brightness.dark,
          ),
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
        home: const DesktopHomeWrapper(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}

class DesktopHomeWrapper extends StatelessWidget {
  const DesktopHomeWrapper({super.key});

  @override
  Widget build(BuildContext context) {
    // 桌面版本的特殊处理
    return Scaffold(
      body: LayoutBuilder(
        builder: (context, constraints) {
          // 如果是桌面环境，使用更宽的布局
          if (constraints.maxWidth > 800) {
            return Row(
              children: [
                // 侧边栏 (可选)
                Container(
                  width: 250,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant,
                    border: Border(
                      right: BorderSide(
                        color: Theme.of(context).dividerColor,
                        width: 1,
                      ),
                    ),
                  ),
                  child: const DesktopSidebar(),
                ),
                // 主内容区域
                const Expanded(
                  child: HomeScreen(),
                ),
              ],
            );
          } else {
            // 移动端布局
            return const HomeScreen();
          }
        },
      ),
    );
  }
}

class DesktopSidebar extends StatelessWidget {
  const DesktopSidebar({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<TodoProvider>(
      builder: (context, todoProvider, child) {
        return Column(
          children: [
            // 应用标题
            Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  Icon(
                    Icons.task_alt,
                    size: 48,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'SuToDo',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '智能TODO管理',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ],
              ),
            ),
            
            const Divider(),
            
            // 快速统计
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '任务概览',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildStatItem(
                    context,
                    '总任务',
                    todoProvider.totalTasks.toString(),
                    Icons.task_alt,
                  ),
                  _buildStatItem(
                    context,
                    '已完成',
                    todoProvider.completedTasksCount.toString(),
                    Icons.check_circle,
                  ),
                  _buildStatItem(
                    context,
                    '进行中',
                    todoProvider.inProgressTasks.length.toString(),
                    Icons.play_circle,
                  ),
                  _buildStatItem(
                    context,
                    '今日到期',
                    todoProvider.getTasksDueToday().length.toString(),
                    Icons.today,
                  ),
                ],
              ),
            ),
            
            const Divider(),
            
            // 快捷操作
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '快捷操作',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        // TODO: 打开添加任务对话框
                      },
                      icon: const Icon(Icons.add),
                      label: const Text('新建任务'),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () {
                        todoProvider.refreshTasks();
                      },
                      icon: const Icon(Icons.refresh),
                      label: const Text('刷新'),
                    ),
                  ),
                ],
              ),
            ),
            
            const Spacer(),
            
            // 版本信息
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'v1.0.0',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodySmall,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
