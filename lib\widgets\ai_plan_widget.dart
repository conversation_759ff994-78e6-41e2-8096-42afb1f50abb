import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../models/todo_task.dart';

class AIPlanWidget extends StatelessWidget {
  final AITaskPlan plan;
  final bool isEditable;
  final Function(AITaskPlan)? onPlanChanged;
  final Function(String)? onStepCompleted;

  const AIPlanWidget({
    super.key,
    required this.plan,
    this.isEditable = false,
    this.onPlanChanged,
    this.onStepCompleted,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Row(
              children: [
                const Icon(Icons.smart_toy, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'AI执行计划',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Chip(
                  label: Text('${plan.totalEstimatedMinutes}分钟'),
                  backgroundColor: Colors.blue.withOpacity(0.1),
                  labelStyle: const TextStyle(
                    color: Colors.blue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // AI推理说明
            if (plan.reasoning.isNotEmpty) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.lightbulb, size: 16, color: Colors.orange),
                        SizedBox(width: 4),
                        Text(
                          'AI分析',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      plan.reasoning,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            
            // 步骤列表
            const Text(
              '执行步骤',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            
            ...plan.steps.asMap().entries.map((entry) {
              final index = entry.key;
              final step = entry.value;
              return _buildStepItem(context, index + 1, step);
            }).toList(),
            
            const SizedBox(height: 16),
            
            // 总结信息
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.withOpacity(0.2)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.info, color: Colors.blue, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '总计 ${plan.steps.length} 个步骤，预计用时 ${plan.totalEstimatedMinutes} 分钟',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        Text(
                          '创建时间: ${DateFormat('MM-dd HH:mm').format(plan.createdAt)}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepItem(BuildContext context, int stepNumber, TaskStep step) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 步骤编号或完成状态
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: step.isCompleted ? Colors.green : Colors.blue,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: step.isCompleted
                  ? const Icon(Icons.check, color: Colors.white, size: 16)
                  : Text(
                      stepNumber.toString(),
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 步骤内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        step.title,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          decoration: step.isCompleted 
                              ? TextDecoration.lineThrough 
                              : null,
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.grey.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${step.estimatedMinutes}分钟',
                        style: const TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 4),
                
                Text(
                  step.description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 13,
                    decoration: step.isCompleted 
                        ? TextDecoration.lineThrough 
                        : null,
                  ),
                ),
                
                if (step.completedAt != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    '完成时间: ${DateFormat('MM-dd HH:mm').format(step.completedAt!)}',
                    style: const TextStyle(
                      fontSize: 11,
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // 操作按钮
          if (!isEditable && onStepCompleted != null) ...[
            const SizedBox(width: 8),
            IconButton(
              onPressed: step.isCompleted 
                  ? null 
                  : () => onStepCompleted!(step.id),
              icon: Icon(
                step.isCompleted ? Icons.check_circle : Icons.radio_button_unchecked,
                color: step.isCompleted ? Colors.green : Colors.grey,
              ),
              iconSize: 20,
            ),
          ],
        ],
      ),
    );
  }
}
