import 'package:flutter/foundation.dart';
import '../models/todo_task.dart';
import '../services/ai_service.dart';

class AIProvider extends ChangeNotifier {
  bool _isGeneratingPlan = false;
  bool _isOptimizingPlan = false;
  String? _lastError;
  AITaskPlan? _currentPlan;

  bool get isGeneratingPlan => _isGeneratingPlan;
  bool get isOptimizingPlan => _isOptimizingPlan;
  bool get isLoading => _isGeneratingPlan || _isOptimizingPlan;
  String? get lastError => _lastError;
  AITaskPlan? get currentPlan => _currentPlan;
  bool get isAIConfigured => AIService.isConfigured();

  Future<AITaskPlan?> generateTaskPlan(String title, String description) async {
    _isGeneratingPlan = true;
    _lastError = null;
    notifyListeners();

    try {
      final plan = await AIService.generateTaskPlan(title, description);
      _currentPlan = plan;
      
      if (plan == null) {
        _lastError = 'AI服务暂时不可用，已生成基础计划';
      }
      
      return plan;
    } catch (e) {
      _lastError = '生成计划时出错: $e';
      print('Error generating plan: $e');
      return null;
    } finally {
      _isGeneratingPlan = false;
      notifyListeners();
    }
  }

  Future<AITaskPlan?> optimizeTaskPlan(AITaskPlan currentPlan, String feedback) async {
    _isOptimizingPlan = true;
    _lastError = null;
    notifyListeners();

    try {
      final optimizedPlan = await AIService.optimizeTaskPlan(currentPlan, feedback);
      
      if (optimizedPlan != null) {
        _currentPlan = optimizedPlan;
      } else {
        _lastError = 'AI服务暂时不可用，无法优化计划';
      }
      
      return optimizedPlan;
    } catch (e) {
      _lastError = '优化计划时出错: $e';
      print('Error optimizing plan: $e');
      return null;
    } finally {
      _isOptimizingPlan = false;
      notifyListeners();
    }
  }

  DateTime? estimateCompletionTime(TodoTask task) {
    return AIService.estimateCompletionTime(task);
  }

  int adjustTimeEstimate(int originalEstimate, List<TodoTask> completedTasks) {
    return AIService.adjustTimeEstimate(originalEstimate, completedTasks);
  }

  void clearCurrentPlan() {
    _currentPlan = null;
    notifyListeners();
  }

  void clearError() {
    _lastError = null;
    notifyListeners();
  }

  // 获取任务进度统计
  Map<String, dynamic> getTaskProgress(TodoTask task) {
    if (task.aiPlan == null) {
      return {
        'totalSteps': 0,
        'completedSteps': 0,
        'progress': 0.0,
        'estimatedTimeRemaining': 0,
      };
    }

    final totalSteps = task.aiPlan!.steps.length;
    final completedSteps = task.aiPlan!.steps.where((step) => step.isCompleted).length;
    final progress = totalSteps > 0 ? completedSteps / totalSteps : 0.0;
    
    // 计算剩余时间
    final remainingSteps = task.aiPlan!.steps.where((step) => !step.isCompleted);
    final estimatedTimeRemaining = remainingSteps.fold<int>(
      0, 
      (sum, step) => sum + step.estimatedMinutes,
    );

    return {
      'totalSteps': totalSteps,
      'completedSteps': completedSteps,
      'progress': progress,
      'estimatedTimeRemaining': estimatedTimeRemaining,
    };
  }

  // 更新步骤完成状态
  AITaskPlan updateStepCompletion(AITaskPlan plan, String stepId, bool isCompleted) {
    final updatedSteps = plan.steps.map((step) {
      if (step.id == stepId) {
        return step.copyWith(
          isCompleted: isCompleted,
          completedAt: isCompleted ? DateTime.now() : null,
        );
      }
      return step;
    }).toList();

    return AITaskPlan(
      steps: updatedSteps,
      totalEstimatedMinutes: plan.totalEstimatedMinutes,
      reasoning: plan.reasoning,
      createdAt: plan.createdAt,
    );
  }

  // 获取下一个未完成的步骤
  TaskStep? getNextStep(AITaskPlan plan) {
    try {
      return plan.steps.firstWhere((step) => !step.isCompleted);
    } catch (e) {
      return null; // 所有步骤都已完成
    }
  }

  // 获取当前正在进行的步骤（基于时间估算）
  TaskStep? getCurrentStep(TodoTask task) {
    if (task.aiPlan == null || task.startedAt == null) return null;

    final elapsedMinutes = DateTime.now().difference(task.startedAt!).inMinutes;
    int accumulatedTime = 0;

    for (final step in task.aiPlan!.steps) {
      accumulatedTime += step.estimatedMinutes;
      if (elapsedMinutes < accumulatedTime) {
        return step;
      }
    }

    // 如果超过了所有步骤的预计时间，返回最后一个未完成的步骤
    return getNextStep(task.aiPlan!);
  }

  // 计算任务延迟情况
  Map<String, dynamic> getTaskDelayInfo(TodoTask task) {
    if (task.aiPlan == null || task.startedAt == null) {
      return {
        'isDelayed': false,
        'delayMinutes': 0,
        'expectedCompletionTime': null,
        'actualProgress': 0.0,
        'expectedProgress': 0.0,
      };
    }

    final elapsedMinutes = DateTime.now().difference(task.startedAt!).inMinutes;
    final expectedProgress = elapsedMinutes / task.aiPlan!.totalEstimatedMinutes;
    final actualProgress = getTaskProgress(task)['progress'] as double;
    
    final isDelayed = actualProgress < expectedProgress && expectedProgress < 1.0;
    final delayMinutes = isDelayed ? 
        ((expectedProgress - actualProgress) * task.aiPlan!.totalEstimatedMinutes).round() : 0;

    final expectedCompletionTime = task.startedAt!.add(
      Duration(minutes: task.aiPlan!.totalEstimatedMinutes),
    );

    return {
      'isDelayed': isDelayed,
      'delayMinutes': delayMinutes,
      'expectedCompletionTime': expectedCompletionTime,
      'actualProgress': actualProgress,
      'expectedProgress': expectedProgress.clamp(0.0, 1.0),
    };
  }
}
