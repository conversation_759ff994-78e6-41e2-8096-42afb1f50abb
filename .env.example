# SuToDo 环境配置示例文件
# 复制此文件为 .env 并填入实际的配置值

# OpenAI API 配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# 应用配置
APP_NAME=SuToDo
APP_VERSION=1.0.0

# 数据库配置
DATABASE_NAME=sutodo.db
DATABASE_VERSION=1

# 通知配置
NOTIFICATION_CHANNEL_ID=sutodo_channel
NOTIFICATION_CHANNEL_NAME=SuToDo Notifications

# 使用说明:
# 1. 将此文件复制为 .env
# 2. 填入你的 OpenAI API 密钥
# 3. 如果不使用AI功能，可以留空 OPENAI_API_KEY
# 4. 运行应用时使用: flutter run --dart-define-from-file=.env
