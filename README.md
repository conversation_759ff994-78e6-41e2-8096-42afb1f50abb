# SuToDo - 智能TODO计划提醒软件

一个基于Flutter开发的智能TODO应用，集成AI功能，能够根据用户输入的任务自动生成详细的执行计划和时间估算。

## 功能特性

### 🤖 AI智能计划
- **任务分解**: AI根据任务描述自动分解为具体可执行的步骤
- **时间估算**: 智能估算每个步骤和整体任务的完成时间
- **计划优化**: 根据用户反馈优化执行计划
- **进度跟踪**: 实时跟踪任务进度和时间偏差

### 📋 任务管理
- **任务创建**: 支持标题、描述、优先级、截止时间、标签等
- **状态管理**: 待开始、进行中、已完成、已取消四种状态
- **优先级**: 低、中、高、紧急四个优先级
- **标签系统**: 灵活的标签分类管理

### 🔍 搜索与筛选
- **全文搜索**: 支持标题、描述、标签的模糊搜索
- **多维筛选**: 按状态、优先级、标签等条件筛选
- **智能排序**: 按优先级、时间、状态智能排序

### 🔔 提醒通知
- **截止提醒**: 任务到期前30分钟和到期时提醒
- **步骤提醒**: AI计划中每个步骤的完成时间提醒
- **本地通知**: 无需网络的本地推送通知

### 📊 数据统计
- **完成率**: 实时显示任务完成进度
- **时间分析**: 分析任务完成时间与预估时间的差异
- **趋势统计**: 任务完成趋势和效率分析

## 技术架构

### 前端框架
- **Flutter**: 跨平台UI框架
- **Provider**: 状态管理
- **Material Design 3**: 现代化UI设计

### 数据存储
- **SQLite**: 本地数据库存储
- **SharedPreferences**: 用户偏好设置

### AI集成
- **OpenAI API**: GPT模型进行任务分析和计划生成
- **本地备用**: AI服务不可用时的本地计划模板

### 通知系统
- **Flutter Local Notifications**: 本地推送通知
- **Timezone**: 时区处理和定时任务

## 🚀 跨平台支持

SuToDo支持在以下平台运行：
- 📱 **Android** - 手机和平板
- 🍎 **iOS** - iPhone和iPad
- 🖥️ **Windows** - Windows 10/11桌面应用
- 💻 **macOS** - macOS桌面应用
- 🐧 **Linux** - Linux桌面应用
- 🌐 **Web** - 浏览器应用

## 安装和运行

### 环境要求
- Flutter SDK >= 3.0.0
- Dart SDK >= 3.0.0
- 平台特定工具：
  - **Android**: Android Studio + Android SDK
  - **iOS**: Xcode (仅限macOS)
  - **Windows**: Visual Studio 2022
  - **macOS**: Xcode
  - **Linux**: 标准开发工具
  - **Web**: Chrome浏览器

### 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd sutodo
   ```

2. **运行设置脚本**
   ```bash
   # Windows
   scripts\run_multiplatform.bat

   # macOS/Linux
   chmod +x scripts/run_multiplatform.sh
   ./scripts/run_multiplatform.sh
   ```

3. **启用桌面支持**
   ```bash
   flutter config --enable-windows-desktop
   flutter config --enable-macos-desktop
   flutter config --enable-linux-desktop
   flutter config --enable-web
   ```

4. **安装依赖**
   ```bash
   flutter pub get
   ```

5. **配置AI服务** (可选)
   ```bash
   # 方法1: 环境变量
   export OPENAI_API_KEY=your_api_key_here

   # 方法2: 运行时指定
   flutter run --dart-define=OPENAI_API_KEY=your_api_key_here
   ```

6. **选择平台运行**
   ```bash
   # 查看可用设备
   flutter devices

   # Android
   flutter run -d android

   # iOS (仅限macOS)
   flutter run -d ios

   # Windows桌面
   flutter run -d windows

   # macOS桌面
   flutter run -d macos

   # Linux桌面
   flutter run -d linux

   # Web浏览器
   flutter run -d web-server --web-port 8080
   ```

### 构建发布版本

**Android**
```bash
# APK文件
flutter build apk --release

# AAB文件(推荐用于Google Play)
flutter build appbundle --release
```

**iOS** (仅限macOS)
```bash
flutter build ios --release
```

**Windows桌面**
```bash
flutter build windows --release
```

**macOS桌面**
```bash
flutter build macos --release
```

**Linux桌面**
```bash
flutter build linux --release
```

**Web应用**
```bash
flutter build web --release
```

## 使用指南

### 创建任务
1. 点击主界面的"新建任务"按钮
2. 填写任务标题和描述
3. 设置优先级、截止时间、标签等
4. 开启"AI智能计划"生成执行计划
5. 保存任务

### AI计划功能
1. 在创建任务时开启"AI智能计划"
2. AI会根据任务内容生成详细步骤
3. 每个步骤包含具体描述和时间估算
4. 可以手动调整或优化计划

### 任务执行
1. 在任务列表中点击"开始任务"
2. 按照AI计划逐步执行
3. 完成每个步骤时进行标记
4. 系统会跟踪进度和时间偏差

### 提醒设置
- 应用会自动为有截止时间的任务设置提醒
- AI计划的步骤也会有对应的时间提醒
- 可在系统设置中管理通知权限

## 项目结构

```
lib/
├── main.dart                 # 应用入口
├── models/                   # 数据模型
│   └── todo_task.dart       # 任务和AI计划模型
├── services/                 # 服务层
│   ├── database_service.dart # 数据库服务
│   ├── ai_service.dart      # AI服务
│   └── notification_service.dart # 通知服务
├── providers/               # 状态管理
│   ├── todo_provider.dart   # 任务状态管理
│   └── ai_provider.dart     # AI功能状态管理
├── screens/                 # 页面
│   ├── home_screen.dart     # 主页面
│   ├── add_task_screen.dart # 添加任务页面
│   └── task_detail_screen.dart # 任务详情页面
└── widgets/                 # 组件
    ├── task_card.dart       # 任务卡片
    ├── task_stats_card.dart # 统计卡片
    ├── ai_plan_widget.dart  # AI计划组件
    ├── search_bar.dart      # 搜索栏
    └── filter_chips.dart    # 筛选组件
```

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目！

### 开发规范
- 遵循Flutter官方代码规范
- 提交前运行 `flutter analyze` 检查代码
- 添加适当的注释和文档
- 测试新功能的兼容性

### 提交流程
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

本项目采用MIT许可证，详见 [LICENSE](LICENSE) 文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至开发者邮箱

---

**SuToDo** - 让AI帮你更好地管理时间和任务！
