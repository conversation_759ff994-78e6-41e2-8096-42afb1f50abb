# SuToDo 跨平台运行指南

## 🌍 支持的平台

- ✅ **Android** - 手机和平板
- ✅ **iOS** - iPhone和iPad
- ✅ **Windows** - Windows 10/11桌面应用
- ✅ **macOS** - macOS桌面应用
- ✅ **Linux** - Linux桌面应用
- ✅ **Web** - 浏览器应用

## 📋 环境要求

### 通用要求
- Flutter SDK >= 3.0.0
- Dart SDK >= 3.0.0
- Git

### Windows 开发环境
```bash
# 必需工具
- Flutter SDK
- Android Studio (Android开发)
- Visual Studio 2022 Community (Windows桌面开发)
- Chrome (Web开发)

# 可选工具
- VS Code + Flutter插件
- Android SDK
```

### macOS 开发环境
```bash
# 必需工具
- Flutter SDK
- Xcode (iOS/macOS开发)
- Android Studio (Android开发)
- Chrome (Web开发)

# 安装命令
brew install --cask flutter
brew install --cask android-studio
brew install --cask visual-studio-code
```

### Linux 开发环境
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install curl git unzip xz-utils zip libglu1-mesa

# 下载Flutter SDK
git clone https://github.com/flutter/flutter.git -b stable
export PATH="$PATH:`pwd`/flutter/bin"
```

## 🚀 快速开始

### 1. 环境检查
```bash
# 检查Flutter环境
flutter doctor

# 查看可用设备
flutter devices

# 启用桌面支持
flutter config --enable-windows-desktop
flutter config --enable-macos-desktop
flutter config --enable-linux-desktop
flutter config --enable-web
```

### 2. 项目设置
```bash
# 克隆项目
git clone <repository-url>
cd sutodo

# 安装依赖
flutter pub get

# 运行跨平台脚本
# Windows
scripts\run_multiplatform.bat

# macOS/Linux
chmod +x scripts/run_multiplatform.sh
./scripts/run_multiplatform.sh
```

## 📱 各平台运行方式

### Android
```bash
# 连接Android设备或启动模拟器
flutter devices

# 运行到Android设备
flutter run -d android

# 构建APK
flutter build apk --release
```

### iOS (仅限macOS)
```bash
# 连接iOS设备或启动模拟器
flutter devices

# 运行到iOS设备
flutter run -d ios

# 构建iOS应用
flutter build ios --release
```

### Windows 桌面
```bash
# 运行Windows桌面应用
flutter run -d windows

# 构建Windows应用
flutter build windows --release
```

### macOS 桌面
```bash
# 运行macOS桌面应用
flutter run -d macos

# 构建macOS应用
flutter build macos --release
```

### Linux 桌面
```bash
# 运行Linux桌面应用
flutter run -d linux

# 构建Linux应用
flutter build linux --release
```

### Web 应用
```bash
# 运行Web应用
flutter run -d web-server --web-port 8080

# 构建Web应用
flutter build web --release
```

## 🔧 平台特定配置

### Android 配置
文件位置: `android/app/src/main/AndroidManifest.xml`
- 已配置网络权限
- 已配置通知权限
- 已配置精确闹钟权限

### iOS 配置
文件位置: `ios/Runner/Info.plist`
- 需要配置通知权限
- 需要配置网络权限

### Windows 配置
- 自动处理依赖
- 支持Windows 10/11
- 默认窗口大小: 1280x720

### macOS 配置
- 需要macOS 10.14+
- 自动处理沙盒权限
- 支持深色模式

## 🎯 推荐的开发流程

### 1. 开发阶段
```bash
# 使用热重载进行开发
flutter run -d <target-device>

# 实时查看更改
# 按 'r' 热重载
# 按 'R' 热重启
# 按 'q' 退出
```

### 2. 测试阶段
```bash
# 运行单元测试
flutter test

# 运行集成测试
flutter drive --target=test_driver/app.dart
```

### 3. 构建发布版本
```bash
# Android
flutter build apk --release
flutter build appbundle --release

# iOS
flutter build ios --release

# Windows
flutter build windows --release

# macOS
flutter build macos --release

# Web
flutter build web --release
```

## 📦 发布指南

### Android 发布
1. 生成签名密钥
2. 配置 `android/app/build.gradle`
3. 构建发布版APK或AAB
4. 上传到Google Play Store

### iOS 发布
1. 配置Apple Developer账号
2. 设置证书和配置文件
3. 构建发布版本
4. 上传到App Store Connect

### Windows 发布
1. 构建Windows应用
2. 创建安装程序(可选)
3. 发布到Microsoft Store或直接分发

### macOS 发布
1. 配置Apple Developer账号
2. 代码签名和公证
3. 发布到Mac App Store或直接分发

### Web 发布
1. 构建Web版本
2. 部署到Web服务器
3. 配置HTTPS和PWA(可选)

## 🐛 常见问题解决

### Flutter Doctor 问题
```bash
# Android许可证问题
flutter doctor --android-licenses

# iOS开发工具问题
sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer

# 桌面支持问题
flutter config --enable-windows-desktop
flutter config --enable-macos-desktop
flutter config --enable-linux-desktop
```

### 依赖问题
```bash
# 清理并重新获取依赖
flutter clean
flutter pub get

# 升级依赖
flutter pub upgrade
```

### 平台特定问题
```bash
# Android构建问题
cd android && ./gradlew clean

# iOS构建问题
cd ios && rm -rf Pods && pod install

# Web构建问题
flutter build web --web-renderer canvaskit
```

## 🎨 UI适配说明

### 响应式设计
- 自动适配不同屏幕尺寸
- 支持横屏和竖屏
- 桌面版本优化布局

### 平台特定UI
- Android: Material Design
- iOS: Cupertino风格(自动适配)
- 桌面: 适配鼠标和键盘操作
- Web: 响应式布局

### 性能优化
- 懒加载长列表
- 图片缓存和压缩
- 数据库查询优化
- 内存管理

## 📞 技术支持

如果在跨平台运行过程中遇到问题:

1. 查看 `flutter doctor` 输出
2. 检查设备连接状态
3. 查看控制台错误信息
4. 参考Flutter官方文档
5. 在GitHub提交Issue

---

**祝你在所有平台上都能顺利运行SuToDo！** 🎉
