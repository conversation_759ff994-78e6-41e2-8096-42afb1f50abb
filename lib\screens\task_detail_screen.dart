import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../providers/todo_provider.dart';
import '../providers/ai_provider.dart';
import '../models/todo_task.dart';
import '../widgets/ai_plan_widget.dart';

class TaskDetailScreen extends StatefulWidget {
  final String taskId;

  const TaskDetailScreen({
    super.key,
    required this.taskId,
  });

  @override
  State<TaskDetailScreen> createState() => _TaskDetailScreenState();
}

class _TaskDetailScreenState extends State<TaskDetailScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<TodoProvider>(
      builder: (context, todoProvider, child) {
        final task = todoProvider.getTaskById(widget.taskId);
        
        if (task == null) {
          return Scaffold(
            appBar: AppBar(title: const Text('任务详情')),
            body: const Center(
              child: Text('任务不存在'),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: Text(task.title),
            actions: [
              PopupMenuButton<String>(
                onSelected: (value) => _handleMenuAction(context, task, value),
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit),
                        SizedBox(width: 8),
                        Text('编辑'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, color: Colors.red),
                        SizedBox(width: 8),
                        Text('删除', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 任务基本信息
                _buildTaskInfo(context, task),
                
                const SizedBox(height: 24),
                
                // 任务状态和操作
                _buildTaskActions(context, task),
                
                const SizedBox(height: 24),
                
                // AI计划
                if (task.aiPlan != null) ...[
                  Consumer<AIProvider>(
                    builder: (context, aiProvider, child) {
                      return AIPlanWidget(
                        plan: task.aiPlan!,
                        onStepCompleted: (stepId) => _completeStep(task, stepId),
                      );
                    },
                  ),
                  const SizedBox(height: 24),
                ],
                
                // 进度信息
                if (task.aiPlan != null) ...[
                  _buildProgressInfo(context, task),
                  const SizedBox(height: 24),
                ],
                
                // 时间信息
                _buildTimeInfo(context, task),
              ],
            ),
          ),
          floatingActionButton: _buildFloatingActionButton(context, task),
        );
      },
    );
  }

  Widget _buildTaskInfo(BuildContext context, TodoTask task) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Text(
              task.title,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // 描述
            Text(
              task.description,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[700],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 优先级和状态
            Row(
              children: [
                _buildPriorityChip(task.priority),
                const SizedBox(width: 8),
                _buildStatusChip(task.status),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // 标签
            if (task.tags.isNotEmpty) ...[
              Wrap(
                spacing: 8,
                children: task.tags.map((tag) {
                  return Chip(
                    label: Text(tag),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTaskActions(BuildContext context, TodoTask task) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '任务操作',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                if (task.status == TaskStatus.pending) ...[
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _startTask(context, task),
                      icon: const Icon(Icons.play_arrow),
                      label: const Text('开始任务'),
                    ),
                  ),
                ],
                if (task.status == TaskStatus.inProgress) ...[
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _completeTask(context, task),
                      icon: const Icon(Icons.check),
                      label: const Text('完成任务'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _pauseTask(context, task),
                      icon: const Icon(Icons.pause),
                      label: const Text('暂停'),
                    ),
                  ),
                ],
                if (task.status == TaskStatus.completed) ...[
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.check_circle, color: Colors.green),
                          SizedBox(width: 8),
                          Text(
                            '任务已完成',
                            style: TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressInfo(BuildContext context, TodoTask task) {
    return Consumer<AIProvider>(
      builder: (context, aiProvider, child) {
        final progress = aiProvider.getTaskProgress(task);
        final delayInfo = aiProvider.getTaskDelayInfo(task);
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '进度信息',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                
                // 进度条
                Row(
                  children: [
                    Expanded(
                      child: LinearProgressIndicator(
                        value: progress['progress'],
                        backgroundColor: Colors.grey[300],
                        valueColor: AlwaysStoppedAnimation<Color>(
                          delayInfo['isDelayed'] ? Colors.red : Colors.blue,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${(progress['progress'] * 100).toInt()}%',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                
                const SizedBox(height: 12),
                
                // 统计信息
                Row(
                  children: [
                    Expanded(
                      child: _buildProgressStat(
                        '已完成步骤',
                        '${progress['completedSteps']}/${progress['totalSteps']}',
                        Icons.check_circle,
                        Colors.green,
                      ),
                    ),
                    Expanded(
                      child: _buildProgressStat(
                        '剩余时间',
                        '${progress['estimatedTimeRemaining']}分钟',
                        Icons.schedule,
                        Colors.blue,
                      ),
                    ),
                  ],
                ),
                
                if (delayInfo['isDelayed']) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.warning, color: Colors.red),
                        const SizedBox(width: 8),
                        Text(
                          '任务进度延迟 ${delayInfo['delayMinutes']} 分钟',
                          style: const TextStyle(
                            color: Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressStat(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildTimeInfo(BuildContext context, TodoTask task) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '时间信息',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildTimeItem('创建时间', dateFormat.format(task.createdAt), Icons.add_circle),
            
            if (task.dueDate != null)
              _buildTimeItem('截止时间', dateFormat.format(task.dueDate!), Icons.schedule),
            
            if (task.startedAt != null)
              _buildTimeItem('开始时间', dateFormat.format(task.startedAt!), Icons.play_arrow),
            
            if (task.completedAt != null)
              _buildTimeItem('完成时间', dateFormat.format(task.completedAt!), Icons.check_circle),
            
            if (task.estimatedCompletionTime != null)
              _buildTimeItem('预计完成', dateFormat.format(task.estimatedCompletionTime!), Icons.flag),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeItem(String label, String time, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(time),
        ],
      ),
    );
  }

  Widget _buildPriorityChip(TaskPriority priority) {
    Color color;
    String text;
    
    switch (priority) {
      case TaskPriority.low:
        color = Colors.grey;
        text = '低优先级';
        break;
      case TaskPriority.medium:
        color = Colors.blue;
        text = '中优先级';
        break;
      case TaskPriority.high:
        color = Colors.orange;
        text = '高优先级';
        break;
      case TaskPriority.urgent:
        color = Colors.red;
        text = '紧急';
        break;
    }
    
    return Chip(
      label: Text(text),
      backgroundColor: color.withOpacity(0.1),
      labelStyle: TextStyle(color: color, fontWeight: FontWeight.bold),
    );
  }

  Widget _buildStatusChip(TaskStatus status) {
    Color color;
    String text;
    IconData icon;
    
    switch (status) {
      case TaskStatus.pending:
        color = Colors.orange;
        text = '待开始';
        icon = Icons.pending;
        break;
      case TaskStatus.inProgress:
        color = Colors.blue;
        text = '进行中';
        icon = Icons.play_arrow;
        break;
      case TaskStatus.completed:
        color = Colors.green;
        text = '已完成';
        icon = Icons.check;
        break;
      case TaskStatus.cancelled:
        color = Colors.red;
        text = '已取消';
        icon = Icons.cancel;
        break;
    }
    
    return Chip(
      avatar: Icon(icon, size: 16, color: Colors.white),
      label: Text(text),
      backgroundColor: color,
      labelStyle: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
    );
  }

  Widget? _buildFloatingActionButton(BuildContext context, TodoTask task) {
    if (task.status == TaskStatus.pending) {
      return FloatingActionButton.extended(
        onPressed: () => _startTask(context, task),
        icon: const Icon(Icons.play_arrow),
        label: const Text('开始任务'),
      );
    } else if (task.status == TaskStatus.inProgress) {
      return FloatingActionButton.extended(
        onPressed: () => _completeTask(context, task),
        icon: const Icon(Icons.check),
        label: const Text('完成任务'),
        backgroundColor: Colors.green,
      );
    }
    return null;
  }

  void _handleMenuAction(BuildContext context, TodoTask task, String action) {
    switch (action) {
      case 'edit':
        // TODO: 实现编辑功能
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('编辑功能即将推出')),
        );
        break;
      case 'delete':
        _showDeleteConfirmation(context, task);
        break;
    }
  }

  void _showDeleteConfirmation(BuildContext context, TodoTask task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除任务'),
        content: Text('确定要删除任务"${task.title}"吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<TodoProvider>().deleteTask(task.id);
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('任务已删除')),
              );
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _startTask(BuildContext context, TodoTask task) {
    context.read<TodoProvider>().startTask(task.id);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('任务已开始')),
    );
  }

  void _completeTask(BuildContext context, TodoTask task) {
    context.read<TodoProvider>().completeTask(task.id);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('任务已完成')),
    );
  }

  void _pauseTask(BuildContext context, TodoTask task) {
    context.read<TodoProvider>().pauseTask(task.id);
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('任务已暂停')),
    );
  }

  void _completeStep(TodoTask task, String stepId) {
    final aiProvider = context.read<AIProvider>();
    final updatedPlan = aiProvider.updateStepCompletion(task.aiPlan!, stepId, true);
    
    final updatedTask = task.copyWith(aiPlan: updatedPlan);
    context.read<TodoProvider>().updateTask(updatedTask);
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('步骤已完成')),
    );
  }
}
