import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:sutodo/main.dart';
import 'package:sutodo/providers/todo_provider.dart';
import 'package:sutodo/providers/ai_provider.dart';
import 'package:sutodo/models/todo_task.dart';

void main() {
  group('SuToDo App Tests', () {
    testWidgets('App should start without crashing', (WidgetTester tester) async {
      // 构建应用
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => TodoProvider()),
            ChangeNotifierProvider(create: (_) => AIProvider()),
          ],
          child: MaterialApp(
            home: Scaffold(
              appBar: AppBar(title: const Text('SuToDo')),
              body: const Center(child: Text('Test App')),
            ),
          ),
        ),
      );

      // 验证应用标题存在
      expect(find.text('SuToDo'), findsOneWidget);
    });

    testWidgets('Should show empty state when no tasks', (WidgetTester tester) async {
      await tester.pumpWidget(
        MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => TodoProvider()),
            ChangeNotifierProvider(create: (_) => AIProvider()),
          ],
          child: MaterialApp(
            home: Scaffold(
              body: Consumer<TodoProvider>(
                builder: (context, todoProvider, child) {
                  if (todoProvider.tasks.isEmpty) {
                    return const Center(
                      child: Text('暂无任务，开始创建你的第一个任务吧！'),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
          ),
        ),
      );

      await tester.pump();
      expect(find.text('暂无任务，开始创建你的第一个任务吧！'), findsOneWidget);
    });
  });

  group('TodoTask Model Tests', () {
    test('TodoTask should be created with correct properties', () {
      final task = TodoTask(
        title: '测试任务',
        description: '这是一个测试任务',
        priority: TaskPriority.high,
      );

      expect(task.title, '测试任务');
      expect(task.description, '这是一个测试任务');
      expect(task.priority, TaskPriority.high);
      expect(task.status, TaskStatus.pending);
      expect(task.id.isNotEmpty, true);
    });

    test('TodoTask should convert to and from Map correctly', () {
      final originalTask = TodoTask(
        title: '测试任务',
        description: '这是一个测试任务',
        priority: TaskPriority.high,
        tags: ['测试', '重要'],
      );

      final taskMap = originalTask.toMap();
      final reconstructedTask = TodoTask.fromMap(taskMap);

      expect(reconstructedTask.title, originalTask.title);
      expect(reconstructedTask.description, originalTask.description);
      expect(reconstructedTask.priority, originalTask.priority);
      expect(reconstructedTask.tags, originalTask.tags);
    });

    test('TodoTask copyWith should work correctly', () {
      final originalTask = TodoTask(
        title: '原始任务',
        description: '原始描述',
        priority: TaskPriority.low,
      );

      final updatedTask = originalTask.copyWith(
        title: '更新任务',
        priority: TaskPriority.high,
      );

      expect(updatedTask.title, '更新任务');
      expect(updatedTask.description, '原始描述'); // 应该保持不变
      expect(updatedTask.priority, TaskPriority.high);
      expect(updatedTask.id, originalTask.id); // ID应该保持不变
    });
  });

  group('AITaskPlan Model Tests', () {
    test('AITaskPlan should be created correctly', () {
      final steps = [
        TaskStep(
          title: '步骤1',
          description: '第一个步骤',
          estimatedMinutes: 30,
        ),
        TaskStep(
          title: '步骤2',
          description: '第二个步骤',
          estimatedMinutes: 45,
        ),
      ];

      final plan = AITaskPlan(
        steps: steps,
        totalEstimatedMinutes: 75,
        reasoning: '这是一个测试计划',
      );

      expect(plan.steps.length, 2);
      expect(plan.totalEstimatedMinutes, 75);
      expect(plan.reasoning, '这是一个测试计划');
    });

    test('AITaskPlan should serialize to JSON correctly', () {
      final steps = [
        TaskStep(
          title: '步骤1',
          description: '第一个步骤',
          estimatedMinutes: 30,
        ),
      ];

      final plan = AITaskPlan(
        steps: steps,
        totalEstimatedMinutes: 30,
        reasoning: '测试计划',
      );

      final json = plan.toJson();
      final reconstructedPlan = AITaskPlan.fromJson(json);

      expect(reconstructedPlan.steps.length, plan.steps.length);
      expect(reconstructedPlan.totalEstimatedMinutes, plan.totalEstimatedMinutes);
      expect(reconstructedPlan.reasoning, plan.reasoning);
    });
  });

  group('TaskStep Model Tests', () {
    test('TaskStep should be created with correct properties', () {
      final step = TaskStep(
        title: '测试步骤',
        description: '这是一个测试步骤',
        estimatedMinutes: 30,
      );

      expect(step.title, '测试步骤');
      expect(step.description, '这是一个测试步骤');
      expect(step.estimatedMinutes, 30);
      expect(step.isCompleted, false);
      expect(step.id.isNotEmpty, true);
    });

    test('TaskStep copyWith should work correctly', () {
      final originalStep = TaskStep(
        title: '原始步骤',
        description: '原始描述',
        estimatedMinutes: 30,
      );

      final updatedStep = originalStep.copyWith(
        isCompleted: true,
        completedAt: DateTime.now(),
      );

      expect(updatedStep.title, originalStep.title);
      expect(updatedStep.isCompleted, true);
      expect(updatedStep.completedAt, isNotNull);
      expect(updatedStep.id, originalStep.id);
    });
  });

  group('Priority and Status Tests', () {
    test('TaskPriority should have correct text representation', () {
      final task1 = TodoTask(title: 'Test', description: 'Test', priority: TaskPriority.low);
      final task2 = TodoTask(title: 'Test', description: 'Test', priority: TaskPriority.medium);
      final task3 = TodoTask(title: 'Test', description: 'Test', priority: TaskPriority.high);
      final task4 = TodoTask(title: 'Test', description: 'Test', priority: TaskPriority.urgent);

      expect(task1.priorityText, '低');
      expect(task2.priorityText, '中');
      expect(task3.priorityText, '高');
      expect(task4.priorityText, '紧急');
    });

    test('TaskStatus should have correct text representation', () {
      final task1 = TodoTask(title: 'Test', description: 'Test', status: TaskStatus.pending);
      final task2 = TodoTask(title: 'Test', description: 'Test', status: TaskStatus.inProgress);
      final task3 = TodoTask(title: 'Test', description: 'Test', status: TaskStatus.completed);
      final task4 = TodoTask(title: 'Test', description: 'Test', status: TaskStatus.cancelled);

      expect(task1.statusText, '待开始');
      expect(task2.statusText, '进行中');
      expect(task3.statusText, '已完成');
      expect(task4.statusText, '已取消');
    });
  });
}
