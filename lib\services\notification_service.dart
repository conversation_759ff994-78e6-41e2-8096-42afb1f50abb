import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import '../models/todo_task.dart';

class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  static Future<void> initialize() async {
    const AndroidInitializationSettings androidSettings =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings iosSettings =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings settings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _notifications.initialize(
      settings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // 请求权限
    await _requestPermissions();
  }

  static Future<void> _requestPermissions() async {
    await _notifications
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.requestNotificationsPermission();

    await _notifications
        .resolvePlatformSpecificImplementation<
            IOSFlutterLocalNotificationsPlugin>()
        ?.requestPermissions(
          alert: true,
          badge: true,
          sound: true,
        );
  }

  static void _onNotificationTapped(NotificationResponse response) {
    // 处理通知点击事件
    print('Notification tapped: ${response.payload}');
  }

  // 立即显示通知
  static Future<void> showNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
      'sutodo_channel',
      'SuToDo Notifications',
      channelDescription: 'Notifications for SuToDo app',
      importance: Importance.high,
      priority: Priority.high,
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails();

    const NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.show(id, title, body, details, payload: payload);
  }

  // 安排定时通知
  static Future<void> scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
      'sutodo_scheduled_channel',
      'SuToDo Scheduled Notifications',
      channelDescription: 'Scheduled notifications for SuToDo app',
      importance: Importance.high,
      priority: Priority.high,
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails();

    const NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _notifications.zonedSchedule(
      id,
      title,
      body,
      tz.TZDateTime.from(scheduledTime, tz.local),
      details,
      payload: payload,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  // 为任务设置提醒
  static Future<void> scheduleTaskReminder(TodoTask task) async {
    if (task.dueDate == null) return;

    final int notificationId = task.id.hashCode;
    
    // 在截止时间前30分钟提醒
    final reminderTime = task.dueDate!.subtract(const Duration(minutes: 30));
    
    if (reminderTime.isAfter(DateTime.now())) {
      await scheduleNotification(
        id: notificationId,
        title: '任务提醒',
        body: '任务 "${task.title}" 将在30分钟后到期',
        scheduledTime: reminderTime,
        payload: task.id,
      );
    }

    // 在截止时间提醒
    if (task.dueDate!.isAfter(DateTime.now())) {
      await scheduleNotification(
        id: notificationId + 1,
        title: '任务到期',
        body: '任务 "${task.title}" 已到期',
        scheduledTime: task.dueDate!,
        payload: task.id,
      );
    }
  }

  // 为AI计划的每个步骤设置提醒
  static Future<void> scheduleAIPlanReminders(TodoTask task) async {
    if (task.aiPlan == null || task.startedAt == null) return;

    DateTime currentTime = task.startedAt!;
    
    for (int i = 0; i < task.aiPlan!.steps.length; i++) {
      final step = task.aiPlan!.steps[i];
      final stepEndTime = currentTime.add(Duration(minutes: step.estimatedMinutes));
      
      if (stepEndTime.isAfter(DateTime.now())) {
        await scheduleNotification(
          id: '${task.id}_step_$i'.hashCode,
          title: '步骤提醒',
          body: '步骤 "${step.title}" 预计完成时间已到',
          scheduledTime: stepEndTime,
          payload: '${task.id}_step_$i',
        );
      }
      
      currentTime = stepEndTime;
    }
  }

  // 取消任务相关的所有通知
  static Future<void> cancelTaskNotifications(String taskId) async {
    final int notificationId = taskId.hashCode;
    
    // 取消主要提醒
    await _notifications.cancel(notificationId);
    await _notifications.cancel(notificationId + 1);
    
    // 取消步骤提醒（假设最多20个步骤）
    for (int i = 0; i < 20; i++) {
      await _notifications.cancel('${taskId}_step_$i'.hashCode);
    }
  }

  // 取消所有通知
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  // 获取待处理的通知
  static Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }
}
