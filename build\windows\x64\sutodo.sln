﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{686CDD3D-567E-34E5-8760-69F90F51934C}"
	ProjectSection(ProjectDependencies) = postProject
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC} = {9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}
		{53276FBE-5FBA-354A-998B-048625DF51D8} = {53276FBE-5FBA-354A-998B-048625DF51D8}
		{D9A0DB6D-2EF6-3198-BEAE-869E1CC8CD8F} = {D9A0DB6D-2EF6-3198-BEAE-869E1CC8CD8F}
		{5DDD870B-AE4B-3817-92DB-470D843E7939} = {5DDD870B-AE4B-3817-92DB-470D843E7939}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{EE4AFEB2-7B8A-3760-99C0-E872A3ECE287}"
	ProjectSection(ProjectDependencies) = postProject
		{686CDD3D-567E-34E5-8760-69F90F51934C} = {686CDD3D-567E-34E5-8760-69F90F51934C}
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC} = {9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "flutter\flutter_assemble.vcxproj", "{EB823A74-576A-3027-A242-FDAFB3F25A92}"
	ProjectSection(ProjectDependencies) = postProject
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC} = {9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "flutter\flutter_wrapper_app.vcxproj", "{53276FBE-5FBA-354A-998B-048625DF51D8}"
	ProjectSection(ProjectDependencies) = postProject
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC} = {9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}
		{EB823A74-576A-3027-A242-FDAFB3F25A92} = {EB823A74-576A-3027-A242-FDAFB3F25A92}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "flutter\flutter_wrapper_plugin.vcxproj", "{D9A0DB6D-2EF6-3198-BEAE-869E1CC8CD8F}"
	ProjectSection(ProjectDependencies) = postProject
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC} = {9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}
		{EB823A74-576A-3027-A242-FDAFB3F25A92} = {EB823A74-576A-3027-A242-FDAFB3F25A92}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "sutodo", "runner\sutodo.vcxproj", "{5DDD870B-AE4B-3817-92DB-470D843E7939}"
	ProjectSection(ProjectDependencies) = postProject
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC} = {9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}
		{EB823A74-576A-3027-A242-FDAFB3F25A92} = {EB823A74-576A-3027-A242-FDAFB3F25A92}
		{53276FBE-5FBA-354A-998B-048625DF51D8} = {53276FBE-5FBA-354A-998B-048625DF51D8}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{686CDD3D-567E-34E5-8760-69F90F51934C}.Debug|x64.ActiveCfg = Debug|x64
		{686CDD3D-567E-34E5-8760-69F90F51934C}.Debug|x64.Build.0 = Debug|x64
		{686CDD3D-567E-34E5-8760-69F90F51934C}.Profile|x64.ActiveCfg = Profile|x64
		{686CDD3D-567E-34E5-8760-69F90F51934C}.Profile|x64.Build.0 = Profile|x64
		{686CDD3D-567E-34E5-8760-69F90F51934C}.Release|x64.ActiveCfg = Release|x64
		{686CDD3D-567E-34E5-8760-69F90F51934C}.Release|x64.Build.0 = Release|x64
		{EE4AFEB2-7B8A-3760-99C0-E872A3ECE287}.Debug|x64.ActiveCfg = Debug|x64
		{EE4AFEB2-7B8A-3760-99C0-E872A3ECE287}.Debug|x64.Build.0 = Debug|x64
		{EE4AFEB2-7B8A-3760-99C0-E872A3ECE287}.Profile|x64.ActiveCfg = Profile|x64
		{EE4AFEB2-7B8A-3760-99C0-E872A3ECE287}.Profile|x64.Build.0 = Profile|x64
		{EE4AFEB2-7B8A-3760-99C0-E872A3ECE287}.Release|x64.ActiveCfg = Release|x64
		{EE4AFEB2-7B8A-3760-99C0-E872A3ECE287}.Release|x64.Build.0 = Release|x64
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}.Debug|x64.ActiveCfg = Debug|x64
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}.Debug|x64.Build.0 = Debug|x64
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}.Profile|x64.ActiveCfg = Profile|x64
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}.Profile|x64.Build.0 = Profile|x64
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}.Release|x64.ActiveCfg = Release|x64
		{9E5CCE48-EA50-3B40-9D5E-5F030EBB66EC}.Release|x64.Build.0 = Release|x64
		{EB823A74-576A-3027-A242-FDAFB3F25A92}.Debug|x64.ActiveCfg = Debug|x64
		{EB823A74-576A-3027-A242-FDAFB3F25A92}.Debug|x64.Build.0 = Debug|x64
		{EB823A74-576A-3027-A242-FDAFB3F25A92}.Profile|x64.ActiveCfg = Profile|x64
		{EB823A74-576A-3027-A242-FDAFB3F25A92}.Profile|x64.Build.0 = Profile|x64
		{EB823A74-576A-3027-A242-FDAFB3F25A92}.Release|x64.ActiveCfg = Release|x64
		{EB823A74-576A-3027-A242-FDAFB3F25A92}.Release|x64.Build.0 = Release|x64
		{53276FBE-5FBA-354A-998B-048625DF51D8}.Debug|x64.ActiveCfg = Debug|x64
		{53276FBE-5FBA-354A-998B-048625DF51D8}.Debug|x64.Build.0 = Debug|x64
		{53276FBE-5FBA-354A-998B-048625DF51D8}.Profile|x64.ActiveCfg = Profile|x64
		{53276FBE-5FBA-354A-998B-048625DF51D8}.Profile|x64.Build.0 = Profile|x64
		{53276FBE-5FBA-354A-998B-048625DF51D8}.Release|x64.ActiveCfg = Release|x64
		{53276FBE-5FBA-354A-998B-048625DF51D8}.Release|x64.Build.0 = Release|x64
		{D9A0DB6D-2EF6-3198-BEAE-869E1CC8CD8F}.Debug|x64.ActiveCfg = Debug|x64
		{D9A0DB6D-2EF6-3198-BEAE-869E1CC8CD8F}.Debug|x64.Build.0 = Debug|x64
		{D9A0DB6D-2EF6-3198-BEAE-869E1CC8CD8F}.Profile|x64.ActiveCfg = Profile|x64
		{D9A0DB6D-2EF6-3198-BEAE-869E1CC8CD8F}.Profile|x64.Build.0 = Profile|x64
		{D9A0DB6D-2EF6-3198-BEAE-869E1CC8CD8F}.Release|x64.ActiveCfg = Release|x64
		{D9A0DB6D-2EF6-3198-BEAE-869E1CC8CD8F}.Release|x64.Build.0 = Release|x64
		{5DDD870B-AE4B-3817-92DB-470D843E7939}.Debug|x64.ActiveCfg = Debug|x64
		{5DDD870B-AE4B-3817-92DB-470D843E7939}.Debug|x64.Build.0 = Debug|x64
		{5DDD870B-AE4B-3817-92DB-470D843E7939}.Profile|x64.ActiveCfg = Profile|x64
		{5DDD870B-AE4B-3817-92DB-470D843E7939}.Profile|x64.Build.0 = Profile|x64
		{5DDD870B-AE4B-3817-92DB-470D843E7939}.Release|x64.ActiveCfg = Release|x64
		{5DDD870B-AE4B-3817-92DB-470D843E7939}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A6A8896B-A679-3FC4-8597-0A07B0DE0C6B}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
