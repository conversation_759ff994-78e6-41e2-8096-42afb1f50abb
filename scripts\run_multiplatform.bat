@echo off
echo ========================================
echo SuToDo 跨平台运行脚本
echo ========================================
echo.

echo 检查Flutter环境...
flutter doctor -v
echo.

echo 请选择运行平台:
echo 1. Android (手机/模拟器)
echo 2. iOS (需要macOS和Xcode)
echo 3. Windows (桌面应用)
echo 4. Web (浏览器)
echo 5. 查看可用设备
echo 6. 启用桌面支持
echo.

set /p choice="请输入选择 (1-6): "

if "%choice%"=="1" (
    echo 运行Android版本...
    flutter run -d android
) else if "%choice%"=="2" (
    echo 运行iOS版本...
    flutter run -d ios
) else if "%choice%"=="3" (
    echo 运行Windows桌面版本...
    flutter run -d windows
) else if "%choice%"=="4" (
    echo 运行Web版本...
    flutter run -d web-server --web-port 8080
    echo 应用将在 http://localhost:8080 运行
) else if "%choice%"=="5" (
    echo 查看可用设备...
    flutter devices
    echo.
    echo 使用 flutter run -d [设备ID] 运行到指定设备
) else if "%choice%"=="6" (
    echo 启用桌面支持...
    flutter config --enable-windows-desktop
    flutter config --enable-macos-desktop
    flutter config --enable-linux-desktop
    flutter config --enable-web
    echo 桌面支持已启用！
) else (
    echo 无效选择！
    goto :eof
)

echo.
echo ========================================
echo 操作完成！
echo ========================================
pause
