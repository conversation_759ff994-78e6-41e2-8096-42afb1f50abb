import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../providers/todo_provider.dart';
import '../providers/ai_provider.dart';
import '../models/todo_task.dart';
import '../widgets/task_card.dart';
import '../widgets/task_stats_card.dart';
import '../widgets/search_bar.dart';
import '../widgets/filter_chips.dart';
import 'add_task_screen.dart';
import 'task_detail_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late TabController _tabController;
  bool _showSearch = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // 加载任务数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<TodoProvider>().loadTasks();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'SuToDo',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        actions: [
          IconButton(
            icon: Icon(_showSearch ? Icons.close : Icons.search),
            onPressed: () {
              setState(() {
                _showSearch = !_showSearch;
                if (!_showSearch) {
                  context.read<TodoProvider>().setSearchQuery('');
                }
              });
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<TodoProvider>().refreshTasks();
            },
          ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(_showSearch ? 120 : 48),
          child: Column(
            children: [
              if (_showSearch) ...[
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: CustomSearchBar(),
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: FilterChips(),
                ),
              ],
              TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(text: '全部'),
                  Tab(text: '待开始'),
                  Tab(text: '进行中'),
                  Tab(text: '已完成'),
                ],
              ),
            ],
          ),
        ),
      ),
      body: Column(
        children: [
          // 统计卡片
          const Padding(
            padding: EdgeInsets.all(16),
            child: TaskStatsCard(),
          ),
          
          // 任务列表
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTaskList(null),
                _buildTaskList(TaskStatus.pending),
                _buildTaskList(TaskStatus.inProgress),
                _buildTaskList(TaskStatus.completed),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const AddTaskScreen(),
            ),
          );
        },
        icon: const Icon(Icons.add),
        label: const Text('新建任务'),
      ),
    );
  }

  Widget _buildTaskList(TaskStatus? status) {
    return Consumer<TodoProvider>(
      builder: (context, todoProvider, child) {
        if (todoProvider.isLoading) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        List<TodoTask> tasks;
        if (status == null) {
          tasks = todoProvider.tasks;
        } else {
          tasks = todoProvider.tasks.where((task) => task.status == status).toList();
        }

        if (tasks.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  _getEmptyIcon(status),
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  _getEmptyMessage(status),
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                ),
                if (status == null || status == TaskStatus.pending) ...[
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const AddTaskScreen(),
                        ),
                      );
                    },
                    icon: const Icon(Icons.add),
                    label: const Text('创建第一个任务'),
                  ),
                ],
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () => todoProvider.refreshTasks(),
          child: AnimationLimiter(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: tasks.length,
              itemBuilder: (context, index) {
                final task = tasks[index];
                return AnimationConfiguration.staggeredList(
                  position: index,
                  duration: const Duration(milliseconds: 375),
                  child: SlideAnimation(
                    verticalOffset: 50.0,
                    child: FadeInAnimation(
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: TaskCard(
                          task: task,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => TaskDetailScreen(taskId: task.id),
                              ),
                            );
                          },
                          onStatusChanged: (newStatus) {
                            _handleStatusChange(task, newStatus);
                          },
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  IconData _getEmptyIcon(TaskStatus? status) {
    switch (status) {
      case TaskStatus.pending:
        return Icons.pending_actions;
      case TaskStatus.inProgress:
        return Icons.play_circle_outline;
      case TaskStatus.completed:
        return Icons.check_circle_outline;
      default:
        return Icons.task_alt;
    }
  }

  String _getEmptyMessage(TaskStatus? status) {
    switch (status) {
      case TaskStatus.pending:
        return '暂无待开始的任务';
      case TaskStatus.inProgress:
        return '暂无进行中的任务';
      case TaskStatus.completed:
        return '暂无已完成的任务';
      default:
        return '暂无任务，开始创建你的第一个任务吧！';
    }
  }

  void _handleStatusChange(TodoTask task, TaskStatus newStatus) {
    final todoProvider = context.read<TodoProvider>();
    
    switch (newStatus) {
      case TaskStatus.inProgress:
        todoProvider.startTask(task.id);
        break;
      case TaskStatus.completed:
        todoProvider.completeTask(task.id);
        break;
      case TaskStatus.pending:
        todoProvider.pauseTask(task.id);
        break;
      case TaskStatus.cancelled:
        // 显示确认对话框
        _showCancelConfirmation(task);
        break;
    }
  }

  void _showCancelConfirmation(TodoTask task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('取消任务'),
        content: Text('确定要取消任务"${task.title}"吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              final updatedTask = task.copyWith(status: TaskStatus.cancelled);
              context.read<TodoProvider>().updateTask(updatedTask);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
