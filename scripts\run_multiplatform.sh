#!/bin/bash

echo "========================================"
echo "SuToDo 跨平台运行脚本"
echo "========================================"
echo

echo "检查Flutter环境..."
flutter doctor -v
echo

echo "请选择运行平台:"
echo "1. Android (手机/模拟器)"
echo "2. iOS (需要macOS和Xcode)"
echo "3. macOS (桌面应用)"
echo "4. Linux (桌面应用)"
echo "5. Web (浏览器)"
echo "6. 查看可用设备"
echo "7. 启用桌面支持"
echo

read -p "请输入选择 (1-7): " choice

case $choice in
    1)
        echo "运行Android版本..."
        flutter run -d android
        ;;
    2)
        echo "运行iOS版本..."
        flutter run -d ios
        ;;
    3)
        echo "运行macOS桌面版本..."
        flutter run -d macos
        ;;
    4)
        echo "运行Linux桌面版本..."
        flutter run -d linux
        ;;
    5)
        echo "运行Web版本..."
        flutter run -d web-server --web-port 8080
        echo "应用将在 http://localhost:8080 运行"
        ;;
    6)
        echo "查看可用设备..."
        flutter devices
        echo
        echo "使用 flutter run -d [设备ID] 运行到指定设备"
        ;;
    7)
        echo "启用桌面支持..."
        flutter config --enable-windows-desktop
        flutter config --enable-macos-desktop
        flutter config --enable-linux-desktop
        flutter config --enable-web
        echo "桌面支持已启用！"
        ;;
    *)
        echo "无效选择！"
        exit 1
        ;;
esac

echo
echo "========================================"
echo "操作完成！"
echo "========================================"
