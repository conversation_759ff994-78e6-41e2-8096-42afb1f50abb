@echo off
title SuToDo - 快速启动
color 0A

echo.
echo ========================================
echo       SuToDo 智能TODO应用
echo ========================================
echo.

echo 🔧 正在初始化Flutter环境...
echo.

REM 设置Flutter路径
set FLUTTER_PATH=C:\flutter\bin
set PATH=%FLUTTER_PATH%;%PATH%

echo 📋 检查Flutter状态...
%FLUTTER_PATH%\flutter.bat --version
if %errorlevel% neq 0 (
    echo.
    echo ❌ Flutter初始化失败！
    echo 💡 请等待Flutter完成初始化，然后重新运行此脚本
    echo.
    pause
    exit /b 1
)

echo.
echo 🔧 启用桌面支持...
%FLUTTER_PATH%\flutter.bat config --enable-windows-desktop
%FLUTTER_PATH%\flutter.bat config --enable-web

echo.
echo 📦 安装项目依赖...
%FLUTTER_PATH%\flutter.bat pub get
if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败！
    pause
    exit /b 1
)

echo.
echo 📱 查看可用设备...
%FLUTTER_PATH%\flutter.bat devices

echo.
echo 🚀 启动选项:
echo   1. Windows桌面应用 (推荐)
echo   2. Web浏览器应用
echo   3. 查看详细信息
echo.

set /p choice="请选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 🚀 启动Windows桌面版...
    echo 💡 应用窗口将在几秒钟后打开
    %FLUTTER_PATH%\flutter.bat run -d windows
) else if "%choice%"=="2" (
    echo.
    echo 🚀 启动Web版本...
    echo 🌐 浏览器将自动打开 http://localhost:8080
    start http://localhost:8080
    %FLUTTER_PATH%\flutter.bat run -d web-server --web-port 8080
) else if "%choice%"=="3" (
    echo.
    echo 📋 系统信息:
    %FLUTTER_PATH%\flutter.bat doctor -v
    echo.
    echo 📱 设备信息:
    %FLUTTER_PATH%\flutter.bat devices -v
) else (
    echo ❌ 无效选择！
)

echo.
echo ========================================
echo 感谢使用 SuToDo！
echo ========================================
pause
