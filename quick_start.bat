@echo off
title SuToDo - Quick Start
color 0A

echo.
echo ========================================
echo       SuToDo Smart TODO App
echo ========================================
echo.

echo Initializing Flutter environment...
echo.

REM Set Flutter path
set FLUTTER_PATH=C:\flutter\bin
set PATH=%FLUTTER_PATH%;%PATH%

echo Checking Flutter status...
%FLUTTER_PATH%\flutter.bat --version
if %errorlevel% neq 0 (
    echo.
    echo Flutter initialization failed!
    echo Please wait for Flutter to complete initialization, then run this script again
    echo.
    pause
    exit /b 1
)

echo.
echo Enabling desktop support...
%FLUTTER_PATH%\flutter.bat config --enable-windows-desktop
%FLUTTER_PATH%\flutter.bat config --enable-web

echo.
echo Installing project dependencies...
%FLUTTER_PATH%\flutter.bat pub get
if %errorlevel% neq 0 (
    echo Dependency installation failed!
    pause
    exit /b 1
)

echo.
echo Checking available devices...
%FLUTTER_PATH%\flutter.bat devices

echo.
echo Launch options:
echo   1. Windows Desktop App (Recommended)
echo   2. Web Browser App
echo   3. View detailed information
echo.

set /p choice="Please choose (1-3): "

if "%choice%"=="1" (
    echo.
    echo Starting Windows Desktop version...
    echo App window will open in a few seconds
    %FLUTTER_PATH%\flutter.bat run -d windows
) else if "%choice%"=="2" (
    echo.
    echo Starting Web version...
    echo Browser will open http://localhost:8080 automatically
    start http://localhost:8080
    %FLUTTER_PATH%\flutter.bat run -d web-server --web-port 8080
) else if "%choice%"=="3" (
    echo.
    echo System information:
    %FLUTTER_PATH%\flutter.bat doctor -v
    echo.
    echo Device information:
    %FLUTTER_PATH%\flutter.bat devices -v
) else (
    echo Invalid choice!
)

echo.
echo ========================================
echo Thank you for using SuToDo!
echo ========================================
pause
