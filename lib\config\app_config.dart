class AppConfig {
  // AI服务配置
  static const String openaiApiKey = String.fromEnvironment(
    'OPENAI_API_KEY',
    defaultValue: '', // 用户需要在环境变量中设置API密钥
  );
  
  static const String openaiBaseUrl = 'https://api.openai.com/v1';
  static const String openaiModel = 'gpt-3.5-turbo';
  
  // 应用配置
  static const String appName = 'SuToDo';
  static const String appVersion = '1.0.0';
  
  // 数据库配置
  static const String databaseName = 'sutodo.db';
  static const int databaseVersion = 1;
  
  // 通知配置
  static const String notificationChannelId = 'sutodo_channel';
  static const String notificationChannelName = 'SuToDo Notifications';
  static const String scheduledChannelId = 'sutodo_scheduled_channel';
  static const String scheduledChannelName = 'SuToDo Scheduled Notifications';
  
  // AI计划配置
  static const int maxPlanSteps = 20;
  static const int minStepMinutes = 5;
  static const int maxStepMinutes = 240;
  static const int defaultTaskMinutes = 60;
  
  // UI配置
  static const int animationDurationMs = 375;
  static const double cardElevation = 2.0;
  static const double borderRadius = 12.0;
  
  // 检查AI服务是否已配置
  static bool get isAIConfigured => openaiApiKey.isNotEmpty;
  
  // 获取完整的API URL
  static String getApiUrl(String endpoint) {
    return '$openaiBaseUrl$endpoint';
  }
  
  // 获取API请求头
  static Map<String, String> get apiHeaders => {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer $openaiApiKey',
  };
}
