import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import '../models/todo_task.dart';
import '../providers/ai_provider.dart';

class TaskCard extends StatelessWidget {
  final TodoTask task;
  final VoidCallback onTap;
  final Function(TaskStatus) onStatusChanged;

  const TaskCard({
    super.key,
    required this.task,
    required this.onTap,
    required this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final aiProvider = context.watch<AIProvider>();
    
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题和状态
              Row(
                children: [
                  Expanded(
                    child: Text(
                      task.title,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        decoration: task.status == TaskStatus.completed
                            ? TextDecoration.lineThrough
                            : null,
                      ),
                    ),
                  ),
                  _buildStatusChip(context),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // 描述
              if (task.description.isNotEmpty) ...[
                Text(
                  task.description,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
              ],
              
              // 优先级和标签
              Row(
                children: [
                  _buildPriorityChip(context),
                  const SizedBox(width: 8),
                  if (task.tags.isNotEmpty) ...[
                    Expanded(
                      child: Wrap(
                        spacing: 4,
                        children: task.tags.take(3).map((tag) {
                          return Chip(
                            label: Text(
                              tag,
                              style: const TextStyle(fontSize: 12),
                            ),
                            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                            visualDensity: VisualDensity.compact,
                          );
                        }).toList(),
                      ),
                    ),
                  ],
                ],
              ),
              
              const SizedBox(height: 12),
              
              // AI计划信息
              if (task.aiPlan != null) ...[
                _buildAIPlanInfo(context, aiProvider),
                const SizedBox(height: 12),
              ],
              
              // 时间信息和操作按钮
              Row(
                children: [
                  Expanded(
                    child: _buildTimeInfo(context),
                  ),
                  _buildActionButtons(context),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    Color color;
    IconData icon;
    
    switch (task.status) {
      case TaskStatus.pending:
        color = Colors.orange;
        icon = Icons.pending;
        break;
      case TaskStatus.inProgress:
        color = Colors.blue;
        icon = Icons.play_arrow;
        break;
      case TaskStatus.completed:
        color = Colors.green;
        icon = Icons.check;
        break;
      case TaskStatus.cancelled:
        color = Colors.red;
        icon = Icons.cancel;
        break;
    }
    
    return Chip(
      avatar: Icon(icon, size: 16, color: Colors.white),
      label: Text(
        task.statusText,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: color,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      visualDensity: VisualDensity.compact,
    );
  }

  Widget _buildPriorityChip(BuildContext context) {
    Color color;
    
    switch (task.priority) {
      case TaskPriority.low:
        color = Colors.grey;
        break;
      case TaskPriority.medium:
        color = Colors.blue;
        break;
      case TaskPriority.high:
        color = Colors.orange;
        break;
      case TaskPriority.urgent:
        color = Colors.red;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        task.priorityText,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildAIPlanInfo(BuildContext context, AIProvider aiProvider) {
    final progress = aiProvider.getTaskProgress(task);
    final delayInfo = aiProvider.getTaskDelayInfo(task);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.smart_toy, size: 16, color: Colors.blue),
              const SizedBox(width: 4),
              const Text(
                'AI计划',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                  fontSize: 12,
                ),
              ),
              const Spacer(),
              Text(
                '${progress['completedSteps']}/${progress['totalSteps']} 步骤',
                style: const TextStyle(fontSize: 12),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress['progress'],
            backgroundColor: Colors.grey[300],
            valueColor: AlwaysStoppedAnimation<Color>(
              delayInfo['isDelayed'] ? Colors.red : Colors.blue,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                '预计剩余: ${progress['estimatedTimeRemaining']}分钟',
                style: const TextStyle(fontSize: 11, color: Colors.grey),
              ),
              if (delayInfo['isDelayed']) ...[
                const Spacer(),
                Text(
                  '延迟 ${delayInfo['delayMinutes']}分钟',
                  style: const TextStyle(fontSize: 11, color: Colors.red),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimeInfo(BuildContext context) {
    final dateFormat = DateFormat('MM/dd HH:mm');
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (task.dueDate != null) ...[
          Row(
            children: [
              Icon(
                Icons.schedule,
                size: 14,
                color: _getDueDateColor(),
              ),
              const SizedBox(width: 4),
              Text(
                '截止: ${dateFormat.format(task.dueDate!)}',
                style: TextStyle(
                  fontSize: 12,
                  color: _getDueDateColor(),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
        if (task.estimatedCompletionTime != null) ...[
          const SizedBox(height: 2),
          Row(
            children: [
              const Icon(Icons.flag, size: 14, color: Colors.green),
              const SizedBox(width: 4),
              Text(
                '预计完成: ${dateFormat.format(task.estimatedCompletionTime!)}',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Color _getDueDateColor() {
    if (task.dueDate == null) return Colors.grey;
    
    final now = DateTime.now();
    final difference = task.dueDate!.difference(now);
    
    if (difference.isNegative) return Colors.red;
    if (difference.inHours < 24) return Colors.orange;
    return Colors.grey;
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (task.status == TaskStatus.pending) ...[
          IconButton(
            onPressed: () => onStatusChanged(TaskStatus.inProgress),
            icon: const Icon(Icons.play_arrow),
            tooltip: '开始任务',
            iconSize: 20,
          ),
        ],
        if (task.status == TaskStatus.inProgress) ...[
          IconButton(
            onPressed: () => onStatusChanged(TaskStatus.completed),
            icon: const Icon(Icons.check),
            tooltip: '完成任务',
            iconSize: 20,
          ),
          IconButton(
            onPressed: () => onStatusChanged(TaskStatus.pending),
            icon: const Icon(Icons.pause),
            tooltip: '暂停任务',
            iconSize: 20,
          ),
        ],
        if (task.status != TaskStatus.completed && task.status != TaskStatus.cancelled) ...[
          IconButton(
            onPressed: () => onStatusChanged(TaskStatus.cancelled),
            icon: const Icon(Icons.close),
            tooltip: '取消任务',
            iconSize: 20,
          ),
        ],
      ],
    );
  }
}
