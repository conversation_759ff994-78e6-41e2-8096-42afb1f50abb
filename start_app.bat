@echo off
title SuToDo App Launcher

echo ========================================
echo       SuToDo Smart TODO App
echo ========================================
echo.

REM Set Flutter path
set FLUTTER_BIN=C:\flutter\bin

echo Checking Flutter...
if not exist "%FLUTTER_BIN%\flutter.bat" (
    echo Flutter not found at %FLUTTER_BIN%
    echo Please make sure Flutter is installed correctly
    pause
    exit /b 1
)

echo Flutter found. Checking version...
"%FLUTTER_BIN%\flutter.bat" --version
if %errorlevel% neq 0 (
    echo Flutter is still initializing. Please wait and try again.
    pause
    exit /b 1
)

echo.
echo Enabling desktop support...
"%FLUTTER_BIN%\flutter.bat" config --enable-windows-desktop

echo.
echo Installing dependencies...
"%FLUTTER_BIN%\flutter.bat" pub get

echo.
echo Checking devices...
"%FLUTTER_BIN%\flutter.bat" devices

echo.
echo Choose launch option:
echo 1 - Windows Desktop (Recommended)
echo 2 - Web Browser
echo 3 - Show Flutter Doctor
echo.

set /p choice="Enter choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Launching Windows Desktop App...
    "%FLUTTER_BIN%\flutter.bat" run -d windows
) else if "%choice%"=="2" (
    echo.
    echo Launching Web App...
    start http://localhost:8080
    "%FLUTTER_BIN%\flutter.bat" run -d web-server --web-port 8080
) else if "%choice%"=="3" (
    echo.
    echo Flutter Doctor Report:
    "%FLUTTER_BIN%\flutter.bat" doctor -v
) else (
    echo Invalid choice!
)

echo.
echo Done!
pause
