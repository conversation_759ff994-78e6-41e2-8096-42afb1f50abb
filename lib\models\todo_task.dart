import 'package:uuid/uuid.dart';

enum TaskPriority { low, medium, high, urgent }

enum TaskStatus { pending, inProgress, completed, cancelled }

class TodoTask {
  final String id;
  final String title;
  final String description;
  final DateTime createdAt;
  final DateTime? dueDate;
  final TaskPriority priority;
  final TaskStatus status;
  final List<String> tags;
  final AITaskPlan? aiPlan;
  final int estimatedMinutes;
  final DateTime? completedAt;
  final DateTime? startedAt;

  TodoTask({
    String? id,
    required this.title,
    required this.description,
    DateTime? createdAt,
    this.dueDate,
    this.priority = TaskPriority.medium,
    this.status = TaskStatus.pending,
    this.tags = const [],
    this.aiPlan,
    this.estimatedMinutes = 0,
    this.completedAt,
    this.startedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now();

  TodoTask copyWith({
    String? title,
    String? description,
    DateTime? dueDate,
    TaskPriority? priority,
    TaskStatus? status,
    List<String>? tags,
    AITaskPlan? aiPlan,
    int? estimatedMinutes,
    DateTime? completedAt,
    DateTime? startedAt,
  }) {
    return TodoTask(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      createdAt: createdAt,
      dueDate: dueDate ?? this.dueDate,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      tags: tags ?? this.tags,
      aiPlan: aiPlan ?? this.aiPlan,
      estimatedMinutes: estimatedMinutes ?? this.estimatedMinutes,
      completedAt: completedAt ?? this.completedAt,
      startedAt: startedAt ?? this.startedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'dueDate': dueDate?.millisecondsSinceEpoch,
      'priority': priority.index,
      'status': status.index,
      'tags': tags.join(','),
      'aiPlan': aiPlan?.toJson(),
      'estimatedMinutes': estimatedMinutes,
      'completedAt': completedAt?.millisecondsSinceEpoch,
      'startedAt': startedAt?.millisecondsSinceEpoch,
    };
  }

  factory TodoTask.fromMap(Map<String, dynamic> map) {
    return TodoTask(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt']),
      dueDate: map['dueDate'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['dueDate'])
          : null,
      priority: TaskPriority.values[map['priority']],
      status: TaskStatus.values[map['status']],
      tags: map['tags'].toString().split(',').where((tag) => tag.isNotEmpty).toList(),
      aiPlan: map['aiPlan'] != null ? AITaskPlan.fromJson(map['aiPlan']) : null,
      estimatedMinutes: map['estimatedMinutes'] ?? 0,
      completedAt: map['completedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['completedAt'])
          : null,
      startedAt: map['startedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['startedAt'])
          : null,
    );
  }

  String get priorityText {
    switch (priority) {
      case TaskPriority.low:
        return '低';
      case TaskPriority.medium:
        return '中';
      case TaskPriority.high:
        return '高';
      case TaskPriority.urgent:
        return '紧急';
    }
  }

  String get statusText {
    switch (status) {
      case TaskStatus.pending:
        return '待开始';
      case TaskStatus.inProgress:
        return '进行中';
      case TaskStatus.completed:
        return '已完成';
      case TaskStatus.cancelled:
        return '已取消';
    }
  }

  DateTime? get estimatedCompletionTime {
    if (aiPlan == null || startedAt == null) return null;
    return startedAt!.add(Duration(minutes: aiPlan!.totalEstimatedMinutes));
  }
}

class AITaskPlan {
  final List<TaskStep> steps;
  final int totalEstimatedMinutes;
  final String reasoning;
  final DateTime createdAt;

  AITaskPlan({
    required this.steps,
    required this.totalEstimatedMinutes,
    required this.reasoning,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'steps': steps.map((step) => step.toJson()).toList(),
      'totalEstimatedMinutes': totalEstimatedMinutes,
      'reasoning': reasoning,
      'createdAt': createdAt.millisecondsSinceEpoch,
    };
  }

  factory AITaskPlan.fromJson(Map<String, dynamic> json) {
    return AITaskPlan(
      steps: (json['steps'] as List)
          .map((stepJson) => TaskStep.fromJson(stepJson))
          .toList(),
      totalEstimatedMinutes: json['totalEstimatedMinutes'],
      reasoning: json['reasoning'],
      createdAt: DateTime.fromMillisecondsSinceEpoch(json['createdAt']),
    );
  }
}

class TaskStep {
  final String id;
  final String title;
  final String description;
  final int estimatedMinutes;
  final bool isCompleted;
  final DateTime? completedAt;

  TaskStep({
    String? id,
    required this.title,
    required this.description,
    required this.estimatedMinutes,
    this.isCompleted = false,
    this.completedAt,
  }) : id = id ?? const Uuid().v4();

  TaskStep copyWith({
    String? title,
    String? description,
    int? estimatedMinutes,
    bool? isCompleted,
    DateTime? completedAt,
  }) {
    return TaskStep(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      estimatedMinutes: estimatedMinutes ?? this.estimatedMinutes,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'estimatedMinutes': estimatedMinutes,
      'isCompleted': isCompleted,
      'completedAt': completedAt?.millisecondsSinceEpoch,
    };
  }

  factory TaskStep.fromJson(Map<String, dynamic> json) {
    return TaskStep(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      estimatedMinutes: json['estimatedMinutes'],
      isCompleted: json['isCompleted'] ?? false,
      completedAt: json['completedAt'] != null
          ? DateTime.fromMillisecondsSinceEpoch(json['completedAt'])
          : null,
    );
  }
}
