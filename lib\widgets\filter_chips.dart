import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/todo_provider.dart';
import '../models/todo_task.dart';

class FilterChips extends StatelessWidget {
  const FilterChips({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<TodoProvider>(
      builder: (context, todoProvider, child) {
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              // 状态筛选
              const Text('状态: ', style: TextStyle(fontWeight: FontWeight.w500)),
              ...TaskStatus.values.map((status) {
                final isSelected = todoProvider.filterStatus == status;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(_getStatusText(status)),
                    selected: isSelected,
                    onSelected: (selected) {
                      todoProvider.setStatusFilter(selected ? status : null);
                    },
                    avatar: Icon(
                      _getStatusIcon(status),
                      size: 16,
                      color: isSelected ? Colors.white : null,
                    ),
                  ),
                );
              }).toList(),
              
              const SizedBox(width: 16),
              
              // 优先级筛选
              const Text('优先级: ', style: TextStyle(fontWeight: FontWeight.w500)),
              ...TaskPriority.values.map((priority) {
                final isSelected = todoProvider.filterPriority == priority;
                return Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: FilterChip(
                    label: Text(_getPriorityText(priority)),
                    selected: isSelected,
                    onSelected: (selected) {
                      todoProvider.setPriorityFilter(selected ? priority : null);
                    },
                    backgroundColor: _getPriorityColor(priority).withOpacity(0.1),
                    selectedColor: _getPriorityColor(priority),
                  ),
                );
              }).toList(),
              
              const SizedBox(width: 16),
              
              // 清除筛选
              if (todoProvider.filterStatus != null || todoProvider.filterPriority != null)
                TextButton.icon(
                  onPressed: () {
                    todoProvider.clearFilters();
                  },
                  icon: const Icon(Icons.clear),
                  label: const Text('清除筛选'),
                ),
            ],
          ),
        );
      },
    );
  }

  String _getStatusText(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return '待开始';
      case TaskStatus.inProgress:
        return '进行中';
      case TaskStatus.completed:
        return '已完成';
      case TaskStatus.cancelled:
        return '已取消';
    }
  }

  IconData _getStatusIcon(TaskStatus status) {
    switch (status) {
      case TaskStatus.pending:
        return Icons.pending;
      case TaskStatus.inProgress:
        return Icons.play_arrow;
      case TaskStatus.completed:
        return Icons.check;
      case TaskStatus.cancelled:
        return Icons.cancel;
    }
  }

  String _getPriorityText(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return '低';
      case TaskPriority.medium:
        return '中';
      case TaskPriority.high:
        return '高';
      case TaskPriority.urgent:
        return '紧急';
    }
  }

  Color _getPriorityColor(TaskPriority priority) {
    switch (priority) {
      case TaskPriority.low:
        return Colors.grey;
      case TaskPriority.medium:
        return Colors.blue;
      case TaskPriority.high:
        return Colors.orange;
      case TaskPriority.urgent:
        return Colors.red;
    }
  }
}
